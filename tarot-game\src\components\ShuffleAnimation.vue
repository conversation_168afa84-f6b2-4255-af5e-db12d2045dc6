<template>
  <div class="shuffle-animation">
    <div class="content">
      <h2 class="title fade-in-up">{{ $t('shuffle.title') }}</h2>
      <p class="description fade-in-up">{{ $t('shuffle.description') }}</p>
      
      <!-- 洗牌动画区域 -->
      <div class="shuffle-container">
        <div 
          v-for="(card, index) in animationCards" 
          :key="index"
          class="shuffle-card"
          :style="getCardStyle(index)"
        >
          <img src="/images/cards/webp/card-back.webp" alt="Tarot Card" />
        </div>
      </div>
      
      <!-- 进度指示器 -->
      <div class="progress-container">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: progress + '%' }"></div>
        </div>
        <p class="progress-text">{{ Math.round(progress) }}%</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const emit = defineEmits(['shuffle-complete'])

const animationCards = ref(Array.from({ length: 12 }, (_, i) => ({ id: i })))
const progress = ref(0)
const shufflePhase = ref(0) // 0: 聚集, 1: 洗牌, 2: 散开

onMounted(() => {
  startShuffleAnimation()
})

const getCardStyle = (index) => {
  const totalCards = animationCards.value.length
  const angle = (360 / totalCards) * index
  const radius = shufflePhase.value === 0 ? 0 : 
                 shufflePhase.value === 1 ? 50 + Math.sin(Date.now() * 0.01 + index) * 30 :
                 150
  
  const x = Math.cos((angle * Math.PI) / 180) * radius
  const y = Math.sin((angle * Math.PI) / 180) * radius
  const rotation = shufflePhase.value === 1 ? 
                   Math.sin(Date.now() * 0.02 + index) * 45 : 
                   angle + (shufflePhase.value === 2 ? Math.random() * 60 - 30 : 0)
  
  return {
    transform: `translate(${x}px, ${y}px) rotate(${rotation}deg)`,
    zIndex: totalCards - index,
    animationDelay: `${index * 0.1}s`
  }
}

const startShuffleAnimation = () => {
  // 第一阶段：卡牌聚集
  setTimeout(() => {
    shufflePhase.value = 1
    startProgressAnimation()
  }, 500)
  
  // 第二阶段：洗牌动画
  setTimeout(() => {
    shufflePhase.value = 2
  }, 2000)
  
  // 完成洗牌
  setTimeout(() => {
    emit('shuffle-complete')
  }, 4000)
}

const startProgressAnimation = () => {
  const duration = 3000 // 3秒
  const startTime = Date.now()
  
  const updateProgress = () => {
    const elapsed = Date.now() - startTime
    const newProgress = Math.min((elapsed / duration) * 100, 100)
    progress.value = newProgress
    
    if (newProgress < 100) {
      requestAnimationFrame(updateProgress)
    }
  }
  
  updateProgress()
}
</script>

<style scoped>
.shuffle-animation {
  position: relative;
  width: 100%;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: radial-gradient(circle at center, var(--bg-tertiary) 0%, var(--bg-primary) 100%);
}

.content {
  text-align: center;
  max-width: 800px;
  padding: 2rem;
}

.title {
  font-size: 3rem;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, var(--accent-gold), var(--accent-primary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: 3rem;
  animation-delay: 0.3s;
}

.shuffle-container {
  position: relative;
  width: 400px;
  height: 400px;
  margin: 0 auto 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.shuffle-card {
  position: absolute;
  width: 80px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
  transition: transform 0.5s ease;
  animation: cardFloat 2s ease-in-out infinite;
}

.shuffle-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@keyframes cardFloat {
  0%, 100% { 
    transform: translateY(0) scale(1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
  }
  50% { 
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 15px 35px rgba(108, 92, 231, 0.3);
  }
}

.progress-container {
  max-width: 300px;
  margin: 0 auto;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-gold));
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-text {
  color: var(--text-secondary);
  font-size: 1rem;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .title {
    font-size: 2rem;
  }
  
  .description {
    font-size: 1rem;
  }
  
  .shuffle-container {
    width: 300px;
    height: 300px;
  }
  
  .shuffle-card {
    width: 60px;
    height: 90px;
  }
}
</style>
