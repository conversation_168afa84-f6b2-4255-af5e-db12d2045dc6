// 塔罗牌数据结构
export const CARD_TYPES = {
  MAJOR: 'major',
  MINOR: 'minor'
}

export const MINOR_SUITS = {
  CUPS: 'cups',
  PENTACLES: 'pentacles', 
  SWORDS: 'swords',
  WANDS: 'wands'
}

export const CARD_POSITIONS = {
  UPRIGHT: 'upright',
  REVERSED: 'reversed'
}

// 大阿卡纳牌 (22张)
export const majorArcana = [
  { id: 'major-00', type: CARD_TYPES.MAJOR, name: '00-fool', image: '/images/cards/webp/major/00-fool.webp' },
  { id: 'major-01', type: CARD_TYPES.MAJOR, name: '01-magician', image: '/images/cards/webp/major/01-magician.webp' },
  { id: 'major-02', type: CARD_TYPES.MAJOR, name: '02-high-priestess', image: '/images/cards/webp/major/02-high-priestess.webp' },
  { id: 'major-03', type: CARD_TYPES.MA<PERSON><PERSON>, name: '03-empress', image: '/images/cards/webp/major/03-empress.webp' },
  { id: 'major-04', type: CARD_TYPES.MAJOR, name: '04-emperor', image: '/images/cards/webp/major/04-emperor.webp' },
  { id: 'major-05', type: CARD_TYPES.MAJOR, name: '05-hierophant', image: '/images/cards/webp/major/05-hierophant.webp' },
  { id: 'major-06', type: CARD_TYPES.MAJOR, name: '06-lovers', image: '/images/cards/webp/major/06-lovers.webp' },
  { id: 'major-07', type: CARD_TYPES.MAJOR, name: '07-chariot', image: '/images/cards/webp/major/07-chariot.webp' },
  { id: 'major-08', type: CARD_TYPES.MAJOR, name: '08-strength', image: '/images/cards/webp/major/08-strength.webp' },
  { id: 'major-09', type: CARD_TYPES.MAJOR, name: '09-hermit', image: '/images/cards/webp/major/09-hermit.webp' },
  { id: 'major-10', type: CARD_TYPES.MAJOR, name: '10-wheel-of-fortune', image: '/images/cards/webp/major/10-wheel-of-fortune.webp' },
  { id: 'major-11', type: CARD_TYPES.MAJOR, name: '11-justice', image: '/images/cards/webp/major/11-justice.webp' },
  { id: 'major-12', type: CARD_TYPES.MAJOR, name: '12-hanged-man', image: '/images/cards/webp/major/12-hanged-man.webp' },
  { id: 'major-13', type: CARD_TYPES.MAJOR, name: '13-death', image: '/images/cards/webp/major/13-death.webp' },
  { id: 'major-14', type: CARD_TYPES.MAJOR, name: '14-temperance', image: '/images/cards/webp/major/14-temperance.webp' },
  { id: 'major-15', type: CARD_TYPES.MAJOR, name: '15-devil', image: '/images/cards/webp/major/15-devil.webp' },
  { id: 'major-16', type: CARD_TYPES.MAJOR, name: '16-tower', image: '/images/cards/webp/major/16-tower.webp' },
  { id: 'major-17', type: CARD_TYPES.MAJOR, name: '17-star', image: '/images/cards/webp/major/17-star.webp' },
  { id: 'major-18', type: CARD_TYPES.MAJOR, name: '18-moon', image: '/images/cards/webp/major/18-moon.webp' },
  { id: 'major-19', type: CARD_TYPES.MAJOR, name: '19-sun', image: '/images/cards/webp/major/19-sun.webp' },
  { id: 'major-20', type: CARD_TYPES.MAJOR, name: '20-judgement', image: '/images/cards/webp/major/20-judgement.webp' },
  { id: 'major-21', type: CARD_TYPES.MAJOR, name: '21-world', image: '/images/cards/webp/major/21-world.webp' }
]

// 小阿卡纳牌生成函数
const createMinorArcanaCards = (suit) => {
  const cards = [
    { name: '00-ace', image: `/images/cards/webp/minor/${suit}/00-ace.webp` },
    { name: '01-two-of-' + suit, image: `/images/cards/webp/minor/${suit}/01-two-of-${suit}.webp` },
    { name: '02-three-of-' + suit, image: `/images/cards/webp/minor/${suit}/02-three-of-${suit}.webp` },
    { name: '03-four-of-' + suit, image: `/images/cards/webp/minor/${suit}/03-four-of-${suit}.webp` },
    { name: '04-five-of-' + suit, image: `/images/cards/webp/minor/${suit}/04-five-of-${suit}.webp` },
    { name: '05-six-of-' + suit, image: `/images/cards/webp/minor/${suit}/05-six-of-${suit}.webp` },
    { name: '06-seven-of-' + suit, image: `/images/cards/webp/minor/${suit}/06-seven-of-${suit}.webp` },
    { name: '07-eight-of-' + suit, image: `/images/cards/webp/minor/${suit}/07-eight-of-${suit}.webp` },
    { name: '08-nine-of-' + suit, image: `/images/cards/webp/minor/${suit}/08-nine-of-${suit}.webp` },
    { name: '09-ten-of-' + suit, image: `/images/cards/webp/minor/${suit}/09-ten-of-${suit}.webp` },
    { name: '10-page-of-' + suit, image: `/images/cards/webp/minor/${suit}/10-page-of-${suit}.webp` },
    { name: '11-knight-of-' + suit, image: `/images/cards/webp/minor/${suit}/11-knight-of-${suit}.webp` },
    { name: '12-queen-of-' + suit, image: `/images/cards/webp/minor/${suit}/12-queen-of-${suit}.webp` },
    { name: '13-king-of-' + suit, image: `/images/cards/webp/minor/${suit}/13-king-of-${suit}.webp` }
  ]
  
  return cards.map((card, index) => ({
    id: `minor-${suit}-${index.toString().padStart(2, '0')}`,
    type: CARD_TYPES.MINOR,
    suit,
    name: card.name,
    image: card.image
  }))
}

// 小阿卡纳牌 (56张)
export const minorArcana = [
  ...createMinorArcanaCards(MINOR_SUITS.CUPS),
  ...createMinorArcanaCards(MINOR_SUITS.PENTACLES),
  ...createMinorArcanaCards(MINOR_SUITS.SWORDS),
  ...createMinorArcanaCards(MINOR_SUITS.WANDS)
]

// 所有塔罗牌 (78张)
export const allTarotCards = [...majorArcana, ...minorArcana]

// 卡牌背面图片
export const CARD_BACK_IMAGE = '/images/cards/webp/card-back.webp'

// 洗牌函数
export const shuffleCards = (cards) => {
  const shuffled = [...cards]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

// 随机分配正位/逆位
export const assignRandomPosition = () => {
  return Math.random() < 0.5 ? CARD_POSITIONS.UPRIGHT : CARD_POSITIONS.REVERSED
}

// 获取卡牌显示名称的函数
export const getCardDisplayName = (card, t) => {
  if (card.type === CARD_TYPES.MAJOR) {
    return t(`cards.major.${card.name}`)
  } else {
    return t(`cards.minor.${card.suit}.${card.name}`)
  }
}
