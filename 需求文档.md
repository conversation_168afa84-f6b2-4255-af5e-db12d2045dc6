我需要完成一款精美的app、小程序，在我当前文件夹下已经存放了关于塔罗牌的相关卡牌图片资源，你可以阅读其文件名，就可以了解它们的含义。
项目架构为：使用vue项目的国际化网站。网站必须有中英文切换的功能，默认语言是英文。

背景和环境描述：

本项目下已经用新建了vue3的项目,你需要在此基础上进行开发。
塔罗牌卡牌总共有22张大阿卡尔牌和56张小阿卡尔牌，

webp文件夹下有webp版本的图片：

webp\major 是22张大阿卡尔牌的路径。

webp\minor下有四个文件夹，是4种小阿卡尔牌的路径，每种下面都有14张牌。

card-back.webp是卡片背面图片。


这个优秀的网站需要完成的功能有：

1.用户无需登录就可以直接进入系统

2.界面要简洁，但要酷炫，界面首页就很清晰的看到开始测试的按钮

3.塔罗牌卡牌的背面图片我已经提供好了，当用户点击开始测试后，界面会有一个洗牌的动画，要足够丝滑酷炫。

4.洗牌完成后，界面上会显示78张卡牌的选择界面，这78张卡牌必须是无序的，用户需要选择三张卡牌。

5.用户选择完毕后，用户抽到的三张卡，你应该用随机的概率显示是何种卡牌，并且要随机让这三张牌是逆位还是正位，(正位（Upright）：牌面图像与牌阵基准方向一致，逆位（Reversed）：牌面图像倒转 180 度呈现)。

6.用户选择完毕后，还需要让用户输入用户的生日，输入要咨询的问题，输入完毕后，可以点击获取结果按钮。

7.点击获取结果按钮后，web系统需要将用户选择的卡牌信息（包括卡牌内容和卡牌正逆位信息）和要咨询的问题，然后根据当前的系统语言（如果是英文，要求大模型用英文回答，如果是中文，要求大模型用中文回答），组成一句话，调用大模型的api接口。

8.大模型的api接口内容可以参考如下（流式响应）：

const question = 'How would you build the tallest building ever?';
const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
  method: 'POST',
  headers: {
    Authorization: `Bearer <OPENROUTER_API_KEY>`,
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    model: 'openai/gpt-4o',
    messages: [{ role: 'user', content: question }],
    stream: true,
  }),
});

const reader = response.body?.getReader();
if (!reader) {
  throw new Error('Response body is not readable');
}

const decoder = new TextDecoder();
let buffer = '';

try {
  while (true) {
    const { done, value } = await reader.read();
    if (done) break;

    // Append new chunk to buffer
    buffer += decoder.decode(value, { stream: true });

    // Process complete lines from buffer
    while (true) {
      const lineEnd = buffer.indexOf('\n');
      if (lineEnd === -1) break;

      const line = buffer.slice(0, lineEnd).trim();
      buffer = buffer.slice(lineEnd + 1);

      if (line.startsWith('data: ')) {
        const data = line.slice(6);
        if (data === '[DONE]') break;

        try {
          const parsed = JSON.parse(data);
          const content = parsed.choices[0].delta.content;
          if (content) {
            console.log(content);
          }
        } catch (e) {
          // Ignore invalid JSON
        }
      }
    }
  }
} finally {
  reader.cancel();
}

9.

我的大模型的 apiKey是：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


10.你调用完毕大模型的api后，能够将api返回的内容解析，并输出到前端界面中，供用户参考。


在过程中的优化需求：

点击获取结果按钮后，web系统需要将用户选择的卡牌信息（包括卡牌内容和卡牌正逆位信息）、用户的生日信息和要咨询的问题，组成一句话，调用大模型的api接口。