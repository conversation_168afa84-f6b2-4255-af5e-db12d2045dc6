<template>
  <div class="user-form">
    <div class="content">
      <div class="header fade-in-up">
        <h2 class="title">{{ $t('form.title') }}</h2>
      </div>

      <div class="form-container">
        <form @submit.prevent="handleSubmit" class="form">
          <!-- 生日输入 -->
          <div class="form-group fade-in-up">
            <label for="birthday" class="form-label">
              {{ $t('form.birthday') }}
            </label>
            <input
              id="birthday"
              v-model="formData.birthday"
              type="date"
              class="form-input"
              :placeholder="$t('form.birthdayPlaceholder')"
              required
            />
          </div>

          <!-- 问题输入 -->
          <div class="form-group fade-in-up">
            <label for="question" class="form-label">
              {{ $t('form.question') }}
            </label>
            <textarea
              id="question"
              v-model="formData.question"
              class="form-textarea"
              :placeholder="$t('form.questionPlaceholder')"
              rows="4"
              required
            ></textarea>
          </div>

          <!-- 按钮组 -->
          <div class="button-group fade-in-up">
            <button 
              type="button" 
              @click="$emit('back')" 
              class="btn btn-secondary"
            >
              {{ $t('common.back') || 'Back' }}
            </button>
            <button 
              type="submit" 
              class="btn btn-primary"
              :disabled="!isFormValid"
            >
              {{ $t('form.getResult') }}
            </button>
          </div>
        </form>

        <!-- 装饰性元素 -->
        <div class="form-decoration">
          <div class="mystical-circle">
            <div class="circle-inner">
              <div class="symbol">✦</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const emit = defineEmits(['form-submit', 'back'])

const formData = ref({
  birthday: '',
  question: ''
})

const isFormValid = computed(() => {
  return formData.value.birthday && formData.value.question.trim().length > 0
})

const handleSubmit = () => {
  if (isFormValid.value) {
    emit('form-submit', { ...formData.value })
  }
}
</script>

<style scoped>
.user-form {
  width: 100%;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: radial-gradient(circle at center, var(--bg-tertiary) 0%, var(--bg-primary) 100%);
}

.content {
  max-width: 600px;
  width: 100%;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, var(--accent-gold), var(--accent-primary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.form-container {
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.form {
  position: relative;
  z-index: 2;
}

.form-group {
  margin-bottom: 2rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--border-color);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
  font-size: 1rem;
  font-family: inherit;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 20px rgba(108, 92, 231, 0.3);
  background: rgba(255, 255, 255, 0.08);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--text-secondary);
}

.button-group {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.btn {
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.btn-primary {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: var(--text-primary);
  border-color: var(--accent-primary);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px var(--shadow-color);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-secondary {
  background: transparent;
  color: var(--text-secondary);
  border-color: var(--border-color);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
  border-color: var(--accent-secondary);
}

.form-decoration {
  position: absolute;
  top: -50px;
  right: -50px;
  width: 100px;
  height: 100px;
  pointer-events: none;
  opacity: 0.3;
}

.mystical-circle {
  width: 100%;
  height: 100%;
  border: 2px solid var(--accent-gold);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: rotate 20s linear infinite;
}

.circle-inner {
  width: 60%;
  height: 60%;
  border: 1px solid var(--accent-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: rotate 15s linear infinite reverse;
}

.symbol {
  font-size: 1.5rem;
  color: var(--accent-gold);
  animation: pulse 3s ease-in-out infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-container {
    padding: 2rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .button-group {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
  
  .form-decoration {
    display: none;
  }
}
</style>
