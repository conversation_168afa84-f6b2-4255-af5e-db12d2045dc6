:root {
  font-family: 'Inter', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* 神秘的深色主题 */
  --bg-primary: #0a0a0f;
  --bg-secondary: #1a1a2e;
  --bg-tertiary: #16213e;
  --text-primary: #ffffff;
  --text-secondary: #b8b8d1;
  --accent-primary: #6c5ce7;
  --accent-secondary: #a29bfe;
  --accent-gold: #fdcb6e;
  --border-color: #2d3748;
  --shadow-color: rgba(108, 92, 231, 0.3);

  color: var(--text-primary);
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  overflow-x: hidden;
}

#app {
  width: 100%;
  min-height: 100vh;
  position: relative;
}

/* 按钮样式 */
.btn {
  border-radius: 12px;
  border: 2px solid var(--accent-primary);
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  font-family: inherit;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px var(--shadow-color);
  border-color: var(--accent-secondary);
}

.btn:active {
  transform: translateY(0);
}

/* 卡片样式 */
.card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* 标题样式 */
h1 {
  font-size: 3.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--accent-gold), var(--accent-primary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  text-shadow: 0 0 30px var(--shadow-color);
}

h2 {
  font-size: 2rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

/* 动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.fade-in-up {
  animation: fadeInUp 0.8s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  h1 {
    font-size: 2.5rem;
  }

  .btn {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }
}
