// AI服务 - 集成OpenRouter API
import { getCardDisplayName, assignRandomPosition, CARD_POSITIONS } from '../data/tarotCards.js'

// const API_KEY = '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'
// const API_URL = 'https://openrouter.ai/api/v1/chat/completions'
const API_KEY = 'sk-IslnGB05pQQBD9y9d8miirlBxBNVovnPNb2rHcg8MlYoYJzF'
const API_URL = 'https://api.chatanywhere.tech/v1/chat/completions'
// 为选中的卡牌分配随机正位/逆位
export const assignCardPositions = (cards) => {
  return cards.map(card => ({
    ...card,
    position: assignRandomPosition(),
    isReversed: assignRandomPosition() === CARD_POSITIONS.REVERSED
  }))
}

// 构建AI提示词
export const buildTarotPrompt = (cards, userInfo, locale, t) => {
  const language = locale === 'zh' ? '中文' : 'English'
  const cardDescriptions = cards.map((card, index) => {
    const cardName = getCardDisplayName(card, t)
    const position = card.isReversed ? t('cards.position.reversed') : t('cards.position.upright')
    return `${index + 1}. ${cardName} (${position})`
  }).join('\n')

  if (locale === 'zh') {
    return `你是一位专业的塔罗牌占卜师。请根据以下信息为用户进行塔罗牌解读：

用户信息：
- 生日：${userInfo.birthday}
- 咨询问题：${userInfo.question}

抽到的卡牌：
${cardDescriptions}

请用中文为用户提供详细的塔罗牌解读，包括：
1. 每张卡牌的含义解释
2. 卡牌之间的关联性
3. 针对用户问题的具体建议
4. 总体运势分析

请以温暖、专业且富有洞察力的语调回答，让用户感受到塔罗牌的神秘力量和指导意义。`
  } else {
    return `You are a professional tarot card reader. Please provide a tarot reading based on the following information:

User Information:
- Birthday: ${userInfo.birthday}
- Question: ${userInfo.question}

Cards drawn:
${cardDescriptions}

Please provide a detailed tarot reading in English, including:
1. Interpretation of each card's meaning
2. Connections between the cards
3. Specific advice for the user's question
4. Overall fortune analysis

Please respond with a warm, professional, and insightful tone that helps the user feel the mystical power and guidance of the tarot cards.`
  }
}

/*
gpt-4o-mini-2024-07-18
o1-preview-ca
claude-3-opus-20240229
gemini-2.5-flash-lite-preview-06-17
gpt-4.1-ca
text-embedding-3-large
o1
gpt-3.5-turbo-1106
claude-3-5-haiku-20241022
gpt-4o-mini-search-preview-2025-03-11
gpt-4-1106-preview
claude-opus-4-20250514-thinking
gpt-4.1-mini-ca
qwen3-coder-480b-a35b-instruct
qwen3-coder-plus
gpt-4o-mini-tts
o3
gpt-3.5-turbo
gpt-4o-ca
gemini-2.0-flash-thinking-exp
gpt-4-turbo-2024-04-09
gpt-4o-mini-ca
gpt-3.5-turbo-instruct
dall-e-3
gpt-4o
o3-mini-2025-01-31
dall-e-2
claude-3-7-sonnet-20250219
o4-mini
gpt-3.5-turbo-16k-0613
gpt-4o-2024-11-20
gpt-4-0125-preview
claude-3-5-sonnet-20240620
claude-sonnet-4-20250514-thinking
gpt-4o-audio-preview-2024-10-01
gpt-4-0613
tts-1-1106
gemini-2.5-flash-preview-05-20
tts-1
gpt-4o-2024-05-13
deepseek-r1
gemini-2.5-pro
gpt-4o
text-embedding-3-small
gemini-exp-1206
o3-mini
deepseek-chat
gpt-4.1
claude-3-5-sonnet-20241022
gemini-2.0-pro-exp-02-05
qwen3-235b-a22b-instruct-2507
gpt-4.1-mini
gpt-4
gpt-3.5-turbo-0125
davinci-002
grok-4-0709
grok-3-deepsearch
gpt-4o-mini
tts-1-hd-1106
claude-opus-4-20250514
gpt-4o-mini
o1-mini-ca
gpt-4.1-nano
gemini-2.5-flash
gpt-4o-mini-audio-preview-2024-12-17
claude-sonnet-4-20250514
gpt-3.5-turbo-instruct-0914
gemini-2.5-pro-exp-03-25
tts-1-hd
gpt-4o-search-preview
gemini-1.5-flash-latest
gpt-4-0125-preview
o1
o3
deepseek-reasoner
gpt-4-ca
gemini-2.0-flash-exp
gemini-2.0-flash
o4-mini-2025-04-16
gpt-image-1
gemini-2.5-flash-preview-04-17
gpt-4.1-mini-2025-04-14
chatgpt-4o-latest
gpt-4o-audio-preview-2024-12-17
o1-mini
qwen3-235b-a22b
gemini-1.5-pro-latest
o1
gpt-4-turbo
gemini-2.5-flash-nothinking
gemini-2.5-pro
kimi-k2-0711-preview
grok-3
gpt-3.5-turbo-ca
gpt-4o-search-preview-2025-03-11
o1-2024-12-17
grok-4
gpt-4o-audio-preview
gpt-4o-mini-search-preview
gpt-4o-mini-audio-preview
gpt-4-turbo-ca
chatgpt-4o-latest-ca
deepseek-v3
gpt-4o-mini-2024-07-18-ca
gpt-4.1-nano-2025-04-14
gpt-4-turbo-preview-ca
deepseek-r1-250528
whisper-1
gpt-4o-transcribe
gemini-2.5-pro
gpt-4.1-nano-ca
gpt-4.1
gpt-4o-mini-transcribe
text-embedding-ada-002
grok-3-reasoner
gpt-3.5-turbo-16k
o1-mini-2024-09-12


*/ 
// 调用AI API - 支持流式和非流式响应
export const getTarotReading = async (cards, userInfo, locale, t, options = {}) => {
  const { isStreaming = true, onProgress } = options

  try {
    // 为卡牌分配正位/逆位
    const cardsWithPositions = assignCardPositions(cards)

    // 构建提示词
    const prompt = buildTarotPrompt(cardsWithPositions, userInfo, locale, t)

    const requestBody = {
      model: 'openai/gpt-4o',
      messages: [{ role: 'user', content: prompt }],
      stream: isStreaming,
    }

    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    let fullResponse = ''

    if (isStreaming) {
      // 流式响应处理
      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('Response body is not readable')
      }

      const decoder = new TextDecoder()
      let buffer = ''

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          // 将新的数据块添加到缓冲区
          buffer += decoder.decode(value, { stream: true })

          // 处理缓冲区中的完整行
          while (true) {
            const lineEnd = buffer.indexOf('\n')
            if (lineEnd === -1) break

            const line = buffer.slice(0, lineEnd).trim()
            buffer = buffer.slice(lineEnd + 1)

            if (line.startsWith('data: ')) {
              const data = line.slice(6)
              if (data === '[DONE]') break

              try {
                const parsed = JSON.parse(data)
                const content = parsed.choices[0].delta.content
                if (content) {
                  fullResponse += content
                  // 调用进度回调函数
                  if (onProgress) {
                    onProgress(fullResponse)
                  }
                }
              } catch (e) {
                // 忽略无效的JSON
                console.warn('Failed to parse JSON:', e)
              }
            }
          }
        }
      } finally {
        reader.cancel()
      }
    } else {
      // 非流式响应处理
      const data = await response.json()
      fullResponse = data.choices[0].message.content

      // 如果有进度回调，直接返回完整结果
      if (onProgress) {
        onProgress(fullResponse)
      }
    }

    return {
      cards: cardsWithPositions,
      reading: fullResponse,
      userInfo
    }
  } catch (error) {
    console.error('Error calling AI API:', error)
    throw new Error('Failed to get tarot reading. Please try again.')
  }
}

// 格式化卡牌信息用于显示
export const formatCardInfo = (card, t) => {
  const cardName = getCardDisplayName(card, t)
  const position = card.isReversed ? t('cards.position.reversed') : t('cards.position.upright')
  return {
    name: cardName,
    position: position,
    isReversed: card.isReversed,
    image: card.image
  }
}
