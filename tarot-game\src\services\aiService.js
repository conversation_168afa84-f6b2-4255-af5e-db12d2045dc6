// AI服务 - 集成OpenRouter API
import { getCardDisplayName, assignRandomPosition, CARD_POSITIONS } from '../data/tarotCards.js'

const API_KEY = '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'
const API_URL = 'https://openrouter.ai/api/v1/chat/completions'

// 为选中的卡牌分配随机正位/逆位
export const assignCardPositions = (cards) => {
  return cards.map(card => ({
    ...card,
    position: assignRandomPosition(),
    isReversed: assignRandomPosition() === CARD_POSITIONS.REVERSED
  }))
}

// 构建AI提示词
export const buildTarotPrompt = (cards, userInfo, locale, t) => {
  const language = locale === 'zh' ? '中文' : 'English'
  const cardDescriptions = cards.map((card, index) => {
    const cardName = getCardDisplayName(card, t)
    const position = card.isReversed ? t('cards.position.reversed') : t('cards.position.upright')
    return `${index + 1}. ${cardName} (${position})`
  }).join('\n')

  if (locale === 'zh') {
    return `你是一位专业的塔罗牌占卜师。请根据以下信息为用户进行塔罗牌解读：

用户信息：
- 生日：${userInfo.birthday}
- 咨询问题：${userInfo.question}

抽到的卡牌：
${cardDescriptions}

请用中文为用户提供详细的塔罗牌解读，包括：
1. 每张卡牌的含义解释
2. 卡牌之间的关联性
3. 针对用户问题的具体建议
4. 总体运势分析

请以温暖、专业且富有洞察力的语调回答，让用户感受到塔罗牌的神秘力量和指导意义。`
  } else {
    return `You are a professional tarot card reader. Please provide a tarot reading based on the following information:

User Information:
- Birthday: ${userInfo.birthday}
- Question: ${userInfo.question}

Cards drawn:
${cardDescriptions}

Please provide a detailed tarot reading in English, including:
1. Interpretation of each card's meaning
2. Connections between the cards
3. Specific advice for the user's question
4. Overall fortune analysis

Please respond with a warm, professional, and insightful tone that helps the user feel the mystical power and guidance of the tarot cards.`
  }
}

// 调用AI API进行流式响应
export const getTarotReading = async (cards, userInfo, locale, t, onProgress) => {
  try {
    // 为卡牌分配正位/逆位
    const cardsWithPositions = assignCardPositions(cards)
    
    // 构建提示词
    const prompt = buildTarotPrompt(cardsWithPositions, userInfo, locale, t)
    
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'openai/gpt-4o',
        messages: [{ role: 'user', content: prompt }],
        stream: true,
      }),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('Response body is not readable')
    }

    const decoder = new TextDecoder()
    let buffer = ''
    let fullResponse = ''

    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        // 将新的数据块添加到缓冲区
        buffer += decoder.decode(value, { stream: true })

        // 处理缓冲区中的完整行
        while (true) {
          const lineEnd = buffer.indexOf('\n')
          if (lineEnd === -1) break

          const line = buffer.slice(0, lineEnd).trim()
          buffer = buffer.slice(lineEnd + 1)

          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') break

            try {
              const parsed = JSON.parse(data)
              const content = parsed.choices[0].delta.content
              if (content) {
                fullResponse += content
                // 调用进度回调函数
                if (onProgress) {
                  onProgress(fullResponse)
                }
              }
            } catch (e) {
              // 忽略无效的JSON
              console.warn('Failed to parse JSON:', e)
            }
          }
        }
      }
    } finally {
      reader.cancel()
    }

    return {
      cards: cardsWithPositions,
      reading: fullResponse,
      userInfo
    }
  } catch (error) {
    console.error('Error calling AI API:', error)
    throw new Error('Failed to get tarot reading. Please try again.')
  }
}

// 格式化卡牌信息用于显示
export const formatCardInfo = (card, t) => {
  const cardName = getCardDisplayName(card, t)
  const position = card.isReversed ? t('cards.position.reversed') : t('cards.position.upright')
  return {
    name: cardName,
    position: position,
    isReversed: card.isReversed,
    image: card.image
  }
}
