<template>
  <div class="result-page">
    <div class="content">
      <div class="header fade-in-up">
        <h2 class="title">{{ $t('result.title') }}</h2>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-section fade-in-up">
        <div class="loading-animation">
          <div class="mystical-loader">
            <div class="loader-circle"></div>
            <div class="loader-symbol">✦</div>
          </div>
        </div>
        <p class="loading-text">{{ $t('result.loading') }}</p>
      </div>

      <!-- 结果展示 -->
      <div v-else class="result-content">
        <!-- 选中的卡牌展示 -->
        <div class="cards-section fade-in-up">
          <h3 class="section-title">{{ $t('result.cards') }}</h3>
          <div class="selected-cards">
            <div 
              v-for="(card, index) in displayCards" 
              :key="card.id"
              class="result-card"
              :class="{ 'reversed': card.isReversed }"
              :style="{ animationDelay: `${index * 0.3}s` }"
            >
              <div class="card-container">
                <div class="card-image">
                  <img :src="card.image" :alt="card.name" />
                </div>
                <div class="card-info">
                  <h4 class="card-name">{{ card.name }}</h4>
                  <p class="card-position" :class="{ 'reversed': card.isReversed }">
                    {{ card.position }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- AI解读结果 -->
        <div class="interpretation-section fade-in-up">
          <h3 class="section-title">{{ $t('result.interpretation') }}</h3>
          <div class="interpretation-content">
            <div class="reading-text" v-html="formattedReading"></div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-section fade-in-up">
          <button @click="$emit('back-to-home')" class="btn btn-primary">
            {{ $t('common.backToHome') || 'Back to Home' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { getTarotReading, formatCardInfo } from '../services/aiService.js'

const props = defineProps({
  selectedCards: {
    type: Array,
    required: true
  },
  userInfo: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['back-to-home'])
const { t, locale } = useI18n()

const isLoading = ref(true)
const readingResult = ref('')
const displayCards = ref([])

const formattedReading = computed(() => {
  return readingResult.value.replace(/\n/g, '<br>')
})

onMounted(async () => {
  try {
    await performTarotReading()
  } catch (error) {
    console.error('Error performing tarot reading:', error)
    readingResult.value = t('error.readingFailed') || 'Failed to get reading. Please try again.'
    isLoading.value = false
  }
})

const performTarotReading = async () => {
  try {
    const options = {
      isStreaming: props.userInfo.isStreaming,
      onProgress: (partialReading) => {
        // 实时更新解读内容
        readingResult.value = partialReading
        if (isLoading.value) {
          isLoading.value = false
        }
      }
    }

    const result = await getTarotReading(
      props.selectedCards,
      props.userInfo,
      locale.value,
      t,
      options
    )

    // 确保最终结果正确设置
    readingResult.value = result.reading
    displayCards.value = result.cards.map(card => formatCardInfo(card, t))
    isLoading.value = false
  } catch (error) {
    throw error
  }
}
</script>

<style scoped>
.result-page {
  width: 100%;
  min-height: 100vh;
  padding: 2rem;
  background: radial-gradient(circle at center, var(--bg-tertiary) 0%, var(--bg-primary) 100%);
}

.content {
  max-width: 1000px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, var(--accent-gold), var(--accent-primary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.loading-section {
  text-align: center;
  padding: 4rem 0;
}

.loading-animation {
  margin-bottom: 2rem;
}

.mystical-loader {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto;
}

.loader-circle {
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top: 3px solid var(--accent-primary);
  border-right: 3px solid var(--accent-secondary);
  border-radius: 50%;
  animation: spin 2s linear infinite;
}

.loader-symbol {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2rem;
  color: var(--accent-gold);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 1.2rem;
  color: var(--text-secondary);
}

.result-content {
  animation: fadeInUp 0.8s ease-out;
}

.section-title {
  font-size: 1.8rem;
  color: var(--accent-gold);
  margin-bottom: 2rem;
  text-align: center;
  font-weight: 600;
}

.cards-section {
  margin-bottom: 4rem;
}

.selected-cards {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.result-card {
  animation: cardReveal 0.8s ease-out;
}

@keyframes cardReveal {
  from {
    opacity: 0;
    transform: translateY(50px) rotateY(90deg);
  }
  to {
    opacity: 1;
    transform: translateY(0) rotateY(0deg);
  }
}

.card-container {
  text-align: center;
  max-width: 200px;
}

.card-image {
  width: 150px;
  height: 225px;
  margin: 0 auto 1rem;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
  transition: transform 0.3s ease;
}

.result-card.reversed .card-image {
  transform: rotate(180deg);
}

.card-image:hover {
  transform: translateY(-5px) scale(1.05);
}

.result-card.reversed .card-image:hover {
  transform: rotate(180deg) translateY(-5px) scale(1.05);
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.card-position {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.card-position.reversed {
  color: var(--accent-secondary);
}

.interpretation-section {
  margin-bottom: 3rem;
}

.interpretation-content {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.reading-text {
  font-size: 1.1rem;
  line-height: 1.8;
  color: var(--text-primary);
  text-align: left;
}

.action-section {
  text-align: center;
}

.btn-primary {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: var(--text-primary);
  border: 2px solid var(--accent-primary);
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px var(--shadow-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .title {
    font-size: 2rem;
  }
  
  .selected-cards {
    gap: 1rem;
  }
  
  .card-image {
    width: 120px;
    height: 180px;
  }
  
  .interpretation-content {
    padding: 1.5rem;
  }
  
  .reading-text {
    font-size: 1rem;
  }
}
</style>
