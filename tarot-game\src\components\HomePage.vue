<template>
  <div class="home-page">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="star star-1"></div>
      <div class="star star-2"></div>
      <div class="star star-3"></div>
      <div class="star star-4"></div>
      <div class="star star-5"></div>
    </div>

    <!-- 主要内容 -->
    <div class="content">
      <div class="hero-section fade-in-up">
        <h1 class="title">{{ $t('app.title') }}</h1>
        <h2 class="subtitle">{{ $t('app.subtitle') }}</h2>
        <p class="description">{{ $t('home.description') }}</p>
        
        <button 
          @click="$emit('start-reading')" 
          class="btn start-btn pulse"
        >
          {{ $t('home.startReading') }}
        </button>
      </div>

      <!-- 装饰性卡牌 -->
      <div class="decorative-cards">
        <div class="card-decoration card-1">
          <img src="/images/cards/webp/card-back.webp" alt="Tarot Card" />
        </div>
        <div class="card-decoration card-2">
          <img src="/images/cards/webp/card-back.webp" alt="Tarot Card" />
        </div>
        <div class="card-decoration card-3">
          <img src="/images/cards/webp/card-back.webp" alt="Tarot Card" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineEmits(['start-reading'])
</script>

<style scoped>
.home-page {
  position: relative;
  width: 100%;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.star {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--accent-gold);
  border-radius: 50%;
  animation: twinkle 3s infinite;
}

.star-1 { top: 20%; left: 10%; animation-delay: 0s; }
.star-2 { top: 30%; right: 15%; animation-delay: 1s; }
.star-3 { top: 60%; left: 20%; animation-delay: 2s; }
.star-4 { bottom: 30%; right: 25%; animation-delay: 0.5s; }
.star-5 { bottom: 20%; left: 30%; animation-delay: 1.5s; }

@keyframes twinkle {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.5); }
}

.content {
  position: relative;
  z-index: 1;
  text-align: center;
  max-width: 800px;
  padding: 2rem;
}

.hero-section {
  margin-bottom: 4rem;
}

.title {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation-delay: 0.2s;
}

.subtitle {
  font-size: 1.5rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  animation-delay: 0.4s;
}

.description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: 3rem;
  line-height: 1.6;
  animation-delay: 0.6s;
}

.start-btn {
  font-size: 1.3rem;
  padding: 1.2rem 3rem;
  animation-delay: 0.8s;
}

.decorative-cards {
  position: relative;
  height: 200px;
  margin-top: 2rem;
}

.card-decoration {
  position: absolute;
  width: 120px;
  height: 180px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  transition: transform 0.3s ease;
}

.card-decoration img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-1 {
  left: 50%;
  top: 0;
  transform: translateX(-50%) rotate(-10deg);
  z-index: 3;
  animation: float 6s ease-in-out infinite;
}

.card-2 {
  left: 30%;
  top: 20px;
  transform: translateX(-50%) rotate(-25deg);
  z-index: 2;
  animation: float 6s ease-in-out infinite 2s;
}

.card-3 {
  right: 30%;
  top: 20px;
  transform: translateX(50%) rotate(25deg);
  z-index: 2;
  animation: float 6s ease-in-out infinite 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0) rotate(var(--rotation)); }
  50% { transform: translateY(-10px) rotate(var(--rotation)); }
}

.card-1 { --rotation: -10deg; }
.card-2 { --rotation: -25deg; }
.card-3 { --rotation: 25deg; }

.card-decoration:hover {
  transform: translateY(-5px) rotate(0deg) scale(1.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .title {
    font-size: 2.5rem;
  }
  
  .subtitle {
    font-size: 1.2rem;
  }
  
  .description {
    font-size: 1rem;
  }
  
  .start-btn {
    font-size: 1.1rem;
    padding: 1rem 2rem;
  }
  
  .decorative-cards {
    height: 150px;
  }
  
  .card-decoration {
    width: 80px;
    height: 120px;
  }
}
</style>
