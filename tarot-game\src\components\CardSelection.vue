<template>
  <div class="card-selection">
    <div class="content">
      <div class="header fade-in-up">
        <h2 class="title">{{ $t('selection.title') }}</h2>
        <p class="description">{{ $t('selection.description') }}</p>
        <div class="selection-counter">
          {{ $t('selection.selected') }}: {{ selectedCards.length }}/3
        </div>
      </div>

      <!-- 卡牌网格 -->
      <div class="cards-grid">
        <div 
          v-for="(card, index) in shuffledCards" 
          :key="card.id"
          class="card-item"
          :class="{ 
            'selected': isCardSelected(card),
            'disabled': selectedCards.length >= 3 && !isCardSelected(card)
          }"
          @click="toggleCard(card)"
          :style="{ animationDelay: `${index * 0.02}s` }"
        >
          <div class="card-inner">
            <div class="card-front">
              <img src="/images/cards/webp/card-back.webp" alt="Tarot Card" />
            </div>
            <div class="selection-indicator" v-if="isCardSelected(card)">
              <span class="selection-number">{{ getSelectionNumber(card) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 继续按钮 -->
      <div class="action-section" v-if="selectedCards.length === 3">
        <button @click="confirmSelection" class="btn continue-btn pulse">
          {{ $t('selection.continue') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { allTarotCards, shuffleCards } from '../data/tarotCards.js'

const emit = defineEmits(['cards-selected'])

const shuffledCards = ref([])
const selectedCards = ref([])

onMounted(() => {
  // 洗牌并创建78张卡牌的随机排列
  shuffledCards.value = shuffleCards(allTarotCards)
})

const isCardSelected = (card) => {
  return selectedCards.value.some(selected => selected.id === card.id)
}

const getSelectionNumber = (card) => {
  const index = selectedCards.value.findIndex(selected => selected.id === card.id)
  return index !== -1 ? index + 1 : ''
}

const toggleCard = (card) => {
  if (isCardSelected(card)) {
    // 取消选择
    selectedCards.value = selectedCards.value.filter(selected => selected.id !== card.id)
  } else if (selectedCards.value.length < 3) {
    // 选择卡牌
    selectedCards.value.push(card)
  }
}

const confirmSelection = () => {
  emit('cards-selected', selectedCards.value)
}
</script>

<style scoped>
.card-selection {
  width: 100%;
  min-height: 100vh;
  padding: 2rem;
  background: radial-gradient(circle at center, var(--bg-tertiary) 0%, var(--bg-primary) 100%);
}

.content {
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, var(--accent-gold), var(--accent-primary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

.selection-counter {
  font-size: 1.1rem;
  color: var(--accent-gold);
  font-weight: 600;
  padding: 0.5rem 1rem;
  background: rgba(253, 203, 110, 0.1);
  border-radius: 20px;
  display: inline-block;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 1rem;
  margin-bottom: 3rem;
  max-height: 60vh;
  overflow-y: auto;
  padding: 1rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.02);
}

.card-item {
  position: relative;
  aspect-ratio: 2/3;
  cursor: pointer;
  transition: all 0.3s ease;
  animation: cardAppear 0.6s ease-out;
  border-radius: 8px;
  overflow: hidden;
}

@keyframes cardAppear {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.card-front {
  width: 100%;
  height: 100%;
}

.card-front img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-item:hover .card-inner {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 8px 25px rgba(108, 92, 231, 0.4);
}

.card-item.selected .card-inner {
  transform: translateY(-8px) scale(1.1);
  box-shadow: 0 12px 30px var(--shadow-color);
  border: 2px solid var(--accent-gold);
}

.card-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.card-item.disabled:hover .card-inner {
  transform: none;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.selection-indicator {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 25px;
  height: 25px;
  background: var(--accent-gold);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: var(--bg-primary);
  font-size: 0.8rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.action-section {
  text-align: center;
  animation: fadeInUp 0.5s ease-out;
}

.continue-btn {
  font-size: 1.2rem;
  padding: 1rem 2.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 0.5rem;
    max-height: 50vh;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .description {
    font-size: 1rem;
  }
  
  .selection-counter {
    font-size: 1rem;
  }
}

/* 滚动条样式 */
.cards-grid::-webkit-scrollbar {
  width: 8px;
}

.cards-grid::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.cards-grid::-webkit-scrollbar-thumb {
  background: var(--accent-primary);
  border-radius: 4px;
}

.cards-grid::-webkit-scrollbar-thumb:hover {
  background: var(--accent-secondary);
}
</style>
