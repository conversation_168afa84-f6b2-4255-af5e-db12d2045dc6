# 塔罗占卜应用 / Tarot Reading App

一个精美的塔罗牌占卜应用，支持中英文双语，具有酷炫的动画效果和AI智能解读功能。

A beautiful tarot card reading application with bilingual support (Chinese/English), cool animations, and AI-powered interpretations.

## 功能特性 / Features

- 🎴 **完整的塔罗牌库**: 包含78张塔罗牌（22张大阿卡纳 + 56张小阿卡纳）
- 🌍 **双语支持**: 中文/英文界面切换
- ✨ **酷炫动画**: 洗牌动画、卡牌翻转效果
- 🤖 **AI智能解读**: 集成OpenRouter API，提供专业的塔罗牌解读
- 📱 **响应式设计**: 支持桌面端和移动端
- 🎨 **神秘主题**: 深色主题配色，营造神秘氛围

## 使用流程 / Usage Flow

1. **首页**: 点击"开始占卜"按钮
2. **洗牌**: 观看酷炫的洗牌动画
3. **选牌**: 从78张卡牌中选择3张
4. **填写信息**: 输入生日和咨询问题
5. **获取结果**: AI生成个性化的塔罗牌解读

## 技术栈 / Tech Stack

- **前端框架**: Vue 3 + Vite
- **国际化**: Vue I18n
- **AI服务**: OpenRouter API (GPT-4)
- **样式**: CSS3 + 自定义动画
- **图片格式**: WebP (优化加载速度)

## 开发和运行 / Development

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

## 项目结构 / Project Structure

```
src/
├── components/          # Vue组件
│   ├── HomePage.vue     # 首页
│   ├── ShuffleAnimation.vue  # 洗牌动画
│   ├── CardSelection.vue     # 卡牌选择
│   ├── UserForm.vue     # 用户信息表单
│   └── ResultPage.vue   # 结果展示
├── data/
│   └── tarotCards.js    # 塔罗牌数据
├── services/
│   └── aiService.js     # AI服务接口
├── locales/             # 国际化语言包
│   ├── en.json         # 英文
│   └── zh.json         # 中文
└── style.css           # 全局样式
```

## 特色功能 / Special Features

### 洗牌动画
- 12张卡牌的动态洗牌效果
- 进度条显示洗牌进度
- 流畅的CSS3动画

### 卡牌选择
- 78张卡牌的网格布局
- 选择状态可视化
- 响应式设计适配不同屏幕

### AI解读
- 流式响应显示解读内容
- 根据用户语言自动切换AI回复语言
- 结合用户生日和问题的个性化解读

### 视觉效果
- 神秘的深色主题
- 渐变色彩搭配
- 悬浮和发光效果
- 卡牌翻转动画（正位/逆位）

## 许可证 / License

MIT License
