/*!
  * vue-i18n v9.14.5
  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
import{createVNode,Text,computed,watch,getCurrentInstance,ref,shallowRef,Fragment,defineComponent,h,effectScope,inject,onMounted,onUnmounted,onBeforeMount,isRef}from"vue";function warn(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const inBrowser="undefined"!=typeof window,makeSymbol=(e,t=!1)=>t?Symbol.for(e):Symbol(e),generateFormatCacheKey=(e,t,r)=>friendlyJSONstringify({l:e,k:t,s:r}),friendlyJSONstringify=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),isNumber=e=>"number"==typeof e&&isFinite(e),isDate=e=>"[object Date]"===toTypeString(e),isRegExp=e=>"[object RegExp]"===toTypeString(e),isEmptyObject=e=>isPlainObject(e)&&0===Object.keys(e).length,assign=Object.assign,_create=Object.create,create=(e=null)=>_create(e);function escapeHtml(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/\//g,"&#x2F;").replace(/=/g,"&#x3D;")}function escapeAttributeValue(e){return e.replace(/&(?![a-zA-Z0-9#]{2,6};)/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function sanitizeTranslatedHtml(e){e=(e=e.replace(/(\w+)\s*=\s*"([^"]*)"/g,((e,t,r)=>`${t}="${escapeAttributeValue(r)}"`))).replace(/(\w+)\s*=\s*'([^']*)'/g,((e,t,r)=>`${t}='${escapeAttributeValue(r)}'`));/\s*on\w+\s*=\s*["']?[^"'>]+["']?/gi.test(e)&&(e=e.replace(/(\s+)(on)(\w+\s*=)/gi,"$1&#111;n$3"));return[/(\s+(?:href|src|action|formaction)\s*=\s*["']?)\s*javascript:/gi,/(style\s*=\s*["'][^"']*url\s*\(\s*)javascript:/gi].forEach((t=>{e=e.replace(t,"$1javascript&#58;")})),e}const hasOwnProperty=Object.prototype.hasOwnProperty;function hasOwn(e,t){return hasOwnProperty.call(e,t)}const isArray=Array.isArray,isFunction=e=>"function"==typeof e,isString=e=>"string"==typeof e,isBoolean=e=>"boolean"==typeof e,isObject=e=>null!==e&&"object"==typeof e,isPromise=e=>isObject(e)&&isFunction(e.then)&&isFunction(e.catch),objectToString=Object.prototype.toString,toTypeString=e=>objectToString.call(e),isPlainObject=e=>{if(!isObject(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t.constructor===Object},toDisplayString=e=>null==e?"":isArray(e)||isPlainObject(e)&&e.toString===objectToString?JSON.stringify(e,null,2):String(e);function join(e,t=""){return e.reduce(((e,r,n)=>0===n?e+r:e+t+r),"")}function incrementer(e){let t=e;return()=>++t}const isNotObjectOrIsArray=e=>!isObject(e)||isArray(e);function deepCopy(e,t){if(isNotObjectOrIsArray(e)||isNotObjectOrIsArray(t))throw new Error("Invalid value");const r=[{src:e,des:t}];for(;r.length;){const{src:e,des:t}=r.pop();Object.keys(e).forEach((n=>{"__proto__"!==n&&(isObject(e[n])&&!isObject(t[n])&&(t[n]=Array.isArray(e[n])?[]:create()),isNotObjectOrIsArray(t[n])||isNotObjectOrIsArray(e[n])?t[n]=e[n]:r.push({src:e[n],des:t[n]}))}))}}function createPosition(e,t,r){return{line:e,column:t,offset:r}}function createLocation(e,t,r){const n={start:e,end:t};return null!=r&&(n.source=r),n}const CompileWarnCodes={USE_MODULO_SYNTAX:1,__EXTEND_POINT__:2};function createCompileWarn(e,t,...r){const n={message:String(e),code:e};return t&&(n.location=t),n}const CompileErrorCodes={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17};function createCompileError(e,t,r={}){const{domain:n,messages:a,args:o}=r,s=new SyntaxError(String(e));return s.code=e,t&&(s.location=t),s.domain=n,s}function defaultOnError(e){throw e}const CHAR_SP=" ",CHAR_CR="\r",CHAR_LF="\n",CHAR_LS=String.fromCharCode(8232),CHAR_PS=String.fromCharCode(8233);function createScanner(e){const t=e;let r=0,n=1,a=1,o=0;const s=e=>t[e]===CHAR_CR&&t[e+1]===CHAR_LF,l=e=>t[e]===CHAR_PS,i=e=>t[e]===CHAR_LS,c=e=>s(e)||(e=>t[e]===CHAR_LF)(e)||l(e)||i(e),u=e=>s(e)||l(e)||i(e)?CHAR_LF:t[e];function m(){return o=0,c(r)&&(n++,a=0),s(r)&&r++,r++,a++,t[r]}return{index:()=>r,line:()=>n,column:()=>a,peekOffset:()=>o,charAt:u,currentChar:()=>u(r),currentPeek:()=>u(r+o),next:m,peek:function(){return s(r+o)&&o++,o++,t[r+o]},reset:function(){r=0,n=1,a=1,o=0},resetPeek:function(e=0){o=e},skipToPeek:function(){const e=r+o;for(;e!==r;)m();o=0}}}const EOF=void 0,DOT=".",LITERAL_DELIMITER="'",ERROR_DOMAIN$1="tokenizer";function createTokenizer(e,t={}){const r=!1!==t.location,n=createScanner(e),a=()=>n.index(),o=()=>createPosition(n.line(),n.column(),n.index()),s=o(),l=a(),i={currentType:14,offset:l,startLoc:s,endLoc:s,lastType:14,lastOffset:l,lastStartLoc:s,lastEndLoc:s,braceNest:0,inLinked:!1,text:""},c=()=>i,{onError:u}=t;function m(e,t,n){e.endLoc=o(),e.currentType=t;const a={type:t};return r&&(a.loc=createLocation(e.startLoc,e.endLoc)),null!=n&&(a.value=n),a}const f=e=>m(e,14);function p(e,t){return e.currentChar()===t?(e.next(),t):(CompileErrorCodes.EXPECTED_TOKEN,o(),"")}function _(e){let t="";for(;e.currentPeek()===CHAR_SP||e.currentPeek()===CHAR_LF;)t+=e.currentPeek(),e.peek();return t}function g(e){const t=_(e);return e.skipToPeek(),t}function d(e){if(e===EOF)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function E(e,t){const{currentType:r}=t;if(2!==r)return!1;_(e);const n=function(e){if(e===EOF)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),n}function b(e){_(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function C(e,t=!0){const r=(t=!1,n="",a=!1)=>{const o=e.currentPeek();return"{"===o?"%"!==n&&t:"@"!==o&&o?"%"===o?(e.peek(),r(t,"%",!0)):"|"===o?!("%"!==n&&!a)||!(n===CHAR_SP||n===CHAR_LF):o===CHAR_SP?(e.peek(),r(!0,CHAR_SP,a)):o!==CHAR_LF||(e.peek(),r(!0,CHAR_LF,a)):"%"===n||t},n=r();return t&&e.resetPeek(),n}function O(e,t){const r=e.currentChar();return r===EOF?EOF:t(r)?(e.next(),r):null}function T(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function L(e){return O(e,T)}function N(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function S(e){return O(e,N)}function v(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function h(e){return O(e,v)}function k(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function P(e){return O(e,k)}function I(e){let t="",r="";for(;t=h(e);)r+=t;return r}function A(e){let t="";for(;;){const r=e.currentChar();if("{"===r||"}"===r||"@"===r||"|"===r||!r)break;if("%"===r){if(!C(e))break;t+=r,e.next()}else if(r===CHAR_SP||r===CHAR_LF)if(C(e))t+=r,e.next();else{if(b(e))break;t+=r,e.next()}else t+=r,e.next()}return t}function y(e){return e!==LITERAL_DELIMITER&&e!==CHAR_LF}function F(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return R(e,t,4);case"U":return R(e,t,6);default:return CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE,o(),""}}function R(e,t,r){p(e,t);let n="";for(let a=0;a<r;a++){const t=P(e);if(!t){CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE,o(),e.currentChar();break}n+=t}return`\\${t}${n}`}function M(e){return"{"!==e&&"}"!==e&&e!==CHAR_SP&&e!==CHAR_LF}function D(e){g(e);const t=p(e,"|");return g(e),t}function x(e,t){let r=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&(CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER,o()),e.next(),r=m(t,2,"{"),g(e),t.braceNest++,r;case"}":return t.braceNest>0&&2===t.currentType&&(CompileErrorCodes.EMPTY_PLACEHOLDER,o()),e.next(),r=m(t,3,"}"),t.braceNest--,t.braceNest>0&&g(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),r;case"@":return t.braceNest>0&&(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,o()),r=j(e,t)||f(t),t.braceNest=0,r;default:{let n=!0,a=!0,s=!0;if(b(e))return t.braceNest>0&&(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,o()),r=m(t,1,D(e)),t.braceNest=0,t.inLinked=!1,r;if(t.braceNest>0&&(5===t.currentType||6===t.currentType||7===t.currentType))return CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,o(),t.braceNest=0,w(e,t);if(n=function(e,t){const{currentType:r}=t;if(2!==r)return!1;_(e);const n=d(e.currentPeek());return e.resetPeek(),n}(e,t))return r=m(t,5,function(e){g(e);let t="",r="";for(;t=S(e);)r+=t;return e.currentChar()===EOF&&(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,o()),r}(e)),g(e),r;if(a=E(e,t))return r=m(t,6,function(e){g(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${I(e)}`):t+=I(e),e.currentChar()===EOF&&(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,o()),t}(e)),g(e),r;if(s=function(e,t){const{currentType:r}=t;if(2!==r)return!1;_(e);const n=e.currentPeek()===LITERAL_DELIMITER;return e.resetPeek(),n}(e,t))return r=m(t,7,function(e){g(e),p(e,"'");let t="",r="";for(;t=O(e,y);)r+="\\"===t?F(e):t;const n=e.currentChar();return n===CHAR_LF||n===EOF?(CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,o(),n===CHAR_LF&&(e.next(),p(e,"'")),r):(p(e,"'"),r)}(e)),g(e),r;if(!n&&!a&&!s)return r=m(t,13,function(e){g(e);let t="",r="";for(;t=O(e,M);)r+=t;return r}(e)),CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER,o(),r.value,g(e),r;break}}return r}function j(e,t){const{currentType:r}=t;let n=null;const a=e.currentChar();switch(8!==r&&9!==r&&12!==r&&10!==r||a!==CHAR_LF&&a!==CHAR_SP||(CompileErrorCodes.INVALID_LINKED_FORMAT,o()),a){case"@":return e.next(),n=m(t,8,"@"),t.inLinked=!0,n;case".":return g(e),e.next(),m(t,9,".");case":":return g(e),e.next(),m(t,10,":");default:return b(e)?(n=m(t,1,D(e)),t.braceNest=0,t.inLinked=!1,n):function(e,t){const{currentType:r}=t;if(8!==r)return!1;_(e);const n="."===e.currentPeek();return e.resetPeek(),n}(e,t)||function(e,t){const{currentType:r}=t;if(8!==r&&12!==r)return!1;_(e);const n=":"===e.currentPeek();return e.resetPeek(),n}(e,t)?(g(e),j(e,t)):function(e,t){const{currentType:r}=t;if(9!==r)return!1;_(e);const n=d(e.currentPeek());return e.resetPeek(),n}(e,t)?(g(e),m(t,12,function(e){let t="",r="";for(;t=L(e);)r+=t;return r}(e))):function(e,t){const{currentType:r}=t;if(10!==r)return!1;const n=()=>{const t=e.currentPeek();return"{"===t?d(e.peek()):!("@"===t||"%"===t||"|"===t||":"===t||"."===t||t===CHAR_SP||!t)&&(t===CHAR_LF?(e.peek(),n()):C(e,!1))},a=n();return e.resetPeek(),a}(e,t)?(g(e),"{"===a?x(e,t)||n:m(t,11,function(e){const t=r=>{const n=e.currentChar();return"{"!==n&&"%"!==n&&"@"!==n&&"|"!==n&&"("!==n&&")"!==n&&n?n===CHAR_SP?r:(r+=n,e.next(),t(r)):r};return t("")}(e))):(8===r&&(CompileErrorCodes.INVALID_LINKED_FORMAT,o()),t.braceNest=0,t.inLinked=!1,w(e,t))}}function w(e,t){let r={type:14};if(t.braceNest>0)return x(e,t)||f(t);if(t.inLinked)return j(e,t)||f(t);switch(e.currentChar()){case"{":return x(e,t)||f(t);case"}":return CompileErrorCodes.UNBALANCED_CLOSING_BRACE,o(),e.next(),m(t,3,"}");case"@":return j(e,t)||f(t);default:{if(b(e))return r=m(t,1,D(e)),t.braceNest=0,t.inLinked=!1,r;const{isModulo:n,hasSpace:a}=function(e){const t=_(e),r="%"===e.currentPeek()&&"{"===e.peek();return e.resetPeek(),{isModulo:r,hasSpace:t.length>0}}(e);if(n)return a?m(t,0,A(e)):m(t,4,function(e){g(e);const t=e.currentChar();return"%"!==t&&(CompileErrorCodes.EXPECTED_TOKEN,o()),e.next(),"%"}(e));if(C(e))return m(t,0,A(e));break}}return r}return{nextToken:function(){const{currentType:e,offset:t,startLoc:r,endLoc:s}=i;return i.lastType=e,i.lastOffset=t,i.lastStartLoc=r,i.lastEndLoc=s,i.offset=a(),i.startLoc=o(),n.currentChar()===EOF?m(i,14):w(n,i)},currentOffset:a,currentPosition:o,context:c}}const ERROR_DOMAIN="parser",KNOWN_ESCAPES=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function fromEscapeSequence(e,t,r){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||r,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function createParser(e={}){const t=!1!==e.location,{onError:r,onWarn:n}=e;function a(e,r,n){const a={type:e};return t&&(a.start=r,a.end=r,a.loc={start:n,end:n}),a}function o(e,r,n,a){a&&(e.type=a),t&&(e.end=r,e.loc&&(e.loc.end=n))}function s(e,t){const r=e.context(),n=a(3,r.offset,r.startLoc);return n.value=t,o(n,e.currentOffset(),e.currentPosition()),n}function l(e,t){const r=e.context(),{lastOffset:n,lastStartLoc:s}=r,l=a(5,n,s);return l.index=parseInt(t,10),e.nextToken(),o(l,e.currentOffset(),e.currentPosition()),l}function i(e,t,r){const n=e.context(),{lastOffset:s,lastStartLoc:l}=n,i=a(4,s,l);return i.key=t,!0===r&&(i.modulo=!0),e.nextToken(),o(i,e.currentOffset(),e.currentPosition()),i}function c(e,t){const r=e.context(),{lastOffset:n,lastStartLoc:s}=r,l=a(9,n,s);return l.value=t.replace(KNOWN_ESCAPES,fromEscapeSequence),e.nextToken(),o(l,e.currentOffset(),e.currentPosition()),l}function u(e){const t=e.context(),r=a(6,t.offset,t.startLoc);let n=e.nextToken();if(9===n.type){const t=function(e){const t=e.nextToken(),r=e.context(),{lastOffset:n,lastStartLoc:s}=r,l=a(8,n,s);return 12!==t.type?(CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER,r.lastStartLoc,l.value="",o(l,n,s),{nextConsumeToken:t,node:l}):(null==t.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,r.lastStartLoc,getTokenCaption(t)),l.value=t.value||"",o(l,e.currentOffset(),e.currentPosition()),{node:l})}(e);r.modifier=t.node,n=t.nextConsumeToken||e.nextToken()}switch(10!==n.type&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(n)),n=e.nextToken(),2===n.type&&(n=e.nextToken()),n.type){case 11:null==n.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(n)),r.key=function(e,t){const r=e.context(),n=a(7,r.offset,r.startLoc);return n.value=t,o(n,e.currentOffset(),e.currentPosition()),n}(e,n.value||"");break;case 5:null==n.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(n)),r.key=i(e,n.value||"");break;case 6:null==n.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(n)),r.key=l(e,n.value||"");break;case 7:null==n.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(n)),r.key=c(e,n.value||"");break;default:{CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY,t.lastStartLoc;const s=e.context(),l=a(7,s.offset,s.startLoc);return l.value="",o(l,s.offset,s.startLoc),r.key=l,o(r,s.offset,s.startLoc),{nextConsumeToken:n,node:r}}}return o(r,e.currentOffset(),e.currentPosition()),{node:r}}function m(e){const t=e.context(),r=a(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);r.items=[];let n=null,m=null;do{const a=n||e.nextToken();switch(n=null,a.type){case 0:null==a.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(a)),r.items.push(s(e,a.value||""));break;case 6:null==a.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(a)),r.items.push(l(e,a.value||""));break;case 4:m=!0;break;case 5:null==a.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(a)),r.items.push(i(e,a.value||"",!!m)),m&&(CompileWarnCodes.USE_MODULO_SYNTAX,t.lastStartLoc,getTokenCaption(a),m=null);break;case 7:null==a.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(a)),r.items.push(c(e,a.value||""));break;case 8:{const t=u(e);r.items.push(t.node),n=t.nextConsumeToken||null;break}}}while(14!==t.currentType&&1!==t.currentType);return o(r,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),r}function f(e){const t=e.context(),{offset:r,startLoc:n}=t,s=m(e);return 14===t.currentType?s:function(e,t,r,n){const s=e.context();let l=0===n.items.length;const i=a(1,t,r);i.cases=[],i.cases.push(n);do{const t=m(e);l||(l=0===t.items.length),i.cases.push(t)}while(14!==s.currentType);return o(i,e.currentOffset(),e.currentPosition()),i}(e,r,n,s)}return{parse:function(r){const n=createTokenizer(r,assign({},e)),s=n.context(),l=a(0,s.offset,s.startLoc);return t&&l.loc&&(l.loc.source=r),l.body=f(n),e.onCacheKey&&(l.cacheKey=e.onCacheKey(r)),14!==s.currentType&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,s.lastStartLoc,r[s.offset]),o(l,n.currentOffset(),n.currentPosition()),l}}}function getTokenCaption(e){if(14===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function createTransformer(e,t={}){const r={ast:e,helpers:new Set};return{context:()=>r,helper:e=>(r.helpers.add(e),e)}}function traverseNodes(e,t){for(let r=0;r<e.length;r++)traverseNode(e[r],t)}function traverseNode(e,t){switch(e.type){case 1:traverseNodes(e.cases,t),t.helper("plural");break;case 2:traverseNodes(e.items,t);break;case 6:traverseNode(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function transform(e,t={}){const r=createTransformer(e);r.helper("normalize"),e.body&&traverseNode(e.body,r);const n=r.context();e.helpers=Array.from(n.helpers)}function optimize(e){const t=e.body;return 2===t.type?optimizeMessageNode(t):t.cases.forEach((e=>optimizeMessageNode(e))),e}function optimizeMessageNode(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let r=0;r<e.items.length;r++){const n=e.items[r];if(3!==n.type&&9!==n.type)break;if(null==n.value)break;t.push(n.value)}if(t.length===e.items.length){e.static=join(t);for(let t=0;t<e.items.length;t++){const r=e.items[t];3!==r.type&&9!==r.type||delete r.value}}}}function minify(e){switch(e.t=e.type,e.type){case 0:{const t=e;minify(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,r=t.cases;for(let e=0;e<r.length;e++)minify(r[e]);t.c=r,delete t.cases;break}case 2:{const t=e,r=t.items;for(let e=0;e<r.length;e++)minify(r[e]);t.i=r,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;minify(t.key),t.k=t.key,delete t.key,t.modifier&&(minify(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function createCodeGenerator(e,t){const{sourceMap:r,filename:n,breakLineCode:a,needIndent:o}=t,s=!1!==t.location,l={filename:n,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:a,needIndent:o,indentLevel:0};s&&e.loc&&(l.source=e.loc.source);function i(e,t){l.code+=e}function c(e,t=!0){const r=t?a:"";i(o?r+"  ".repeat(e):r)}return{context:()=>l,push:i,indent:function(e=!0){const t=++l.indentLevel;e&&c(t)},deindent:function(e=!0){const t=--l.indentLevel;e&&c(t)},newline:function(){c(l.indentLevel)},helper:e=>`_${e}`,needIndent:()=>l.needIndent}}function generateLinkedNode(e,t){const{helper:r}=e;e.push(`${r("linked")}(`),generateNode(e,t.key),t.modifier?(e.push(", "),generateNode(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function generateMessageNode(e,t){const{helper:r,needIndent:n}=e;e.push(`${r("normalize")}([`),e.indent(n());const a=t.items.length;for(let o=0;o<a&&(generateNode(e,t.items[o]),o!==a-1);o++)e.push(", ");e.deindent(n()),e.push("])")}function generatePluralNode(e,t){const{helper:r,needIndent:n}=e;if(t.cases.length>1){e.push(`${r("plural")}([`),e.indent(n());const a=t.cases.length;for(let r=0;r<a&&(generateNode(e,t.cases[r]),r!==a-1);r++)e.push(", ");e.deindent(n()),e.push("])")}}function generateResource(e,t){t.body?generateNode(e,t.body):e.push("null")}function generateNode(e,t){const{helper:r}=e;switch(t.type){case 0:generateResource(e,t);break;case 1:generatePluralNode(e,t);break;case 2:generateMessageNode(e,t);break;case 6:generateLinkedNode(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${r("interpolate")}(${r("list")}(${t.index}))`,t);break;case 4:e.push(`${r("interpolate")}(${r("named")}(${JSON.stringify(t.key)}))`,t)}}const generate=(e,t={})=>{const r=isString(t.mode)?t.mode:"normal",n=isString(t.filename)?t.filename:"message.intl",a=!!t.sourceMap,o=null!=t.breakLineCode?t.breakLineCode:"arrow"===r?";":"\n",s=t.needIndent?t.needIndent:"arrow"!==r,l=e.helpers||[],i=createCodeGenerator(e,{mode:r,filename:n,sourceMap:a,breakLineCode:o,needIndent:s});i.push("normal"===r?"function __msg__ (ctx) {":"(ctx) => {"),i.indent(s),l.length>0&&(i.push(`const { ${join(l.map((e=>`${e}: _${e}`)),", ")} } = ctx`),i.newline()),i.push("return "),generateNode(i,e),i.deindent(s),i.push("}"),delete e.helpers;const{code:c,map:u}=i.context();return{ast:e,code:c,map:u?u.toJSON():void 0}};function baseCompile$1(e,t={}){const r=assign({},t),n=!!r.jit,a=!!r.minify,o=null==r.optimize||r.optimize,s=createParser(r).parse(e);return n?(o&&optimize(s),a&&minify(s),{ast:s,code:""}):(transform(s,r),generate(s,r))}function isMessageAST(e){return isObject(e)&&0===resolveType(e)&&(hasOwn(e,"b")||hasOwn(e,"body"))}const PROPS_BODY=["b","body"];function resolveBody(e){return resolveProps(e,PROPS_BODY)}const PROPS_CASES=["c","cases"];function resolveCases(e){return resolveProps(e,PROPS_CASES,[])}const PROPS_STATIC=["s","static"];function resolveStatic(e){return resolveProps(e,PROPS_STATIC)}const PROPS_ITEMS=["i","items"];function resolveItems(e){return resolveProps(e,PROPS_ITEMS,[])}const PROPS_TYPE=["t","type"];function resolveType(e){return resolveProps(e,PROPS_TYPE)}const PROPS_VALUE=["v","value"];function resolveValue$1(e,t){const r=resolveProps(e,PROPS_VALUE);if(null!=r)return r;throw createUnhandleNodeError(t)}const PROPS_MODIFIER=["m","modifier"];function resolveLinkedModifier(e){return resolveProps(e,PROPS_MODIFIER)}const PROPS_KEY=["k","key"];function resolveLinkedKey(e){const t=resolveProps(e,PROPS_KEY);if(t)return t;throw createUnhandleNodeError(6)}function resolveProps(e,t,r){for(let n=0;n<t.length;n++){const r=t[n];if(hasOwn(e,r)&&null!=e[r])return e[r]}return r}const AST_NODE_PROPS_KEYS=[...PROPS_BODY,...PROPS_CASES,...PROPS_STATIC,...PROPS_ITEMS,...PROPS_KEY,...PROPS_MODIFIER,...PROPS_VALUE,...PROPS_TYPE];function createUnhandleNodeError(e){return new Error(`unhandled node type: ${e}`)}const pathStateMachine=[];pathStateMachine[0]={w:[0],i:[3,0],"[":[4],o:[7]},pathStateMachine[1]={w:[1],".":[2],"[":[4],o:[7]},pathStateMachine[2]={w:[2],i:[3,0],0:[3,0]},pathStateMachine[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},pathStateMachine[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},pathStateMachine[5]={"'":[4,0],o:8,l:[5,0]},pathStateMachine[6]={'"':[4,0],o:8,l:[6,0]};const literalValueRE=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function isLiteral(e){return literalValueRE.test(e)}function stripQuotes(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}function getPathCharType(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function formatSubPath(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(isLiteral(t)?stripQuotes(t):"*"+t)}function parse(e){const t=[];let r,n,a,o,s,l,i,c=-1,u=0,m=0;const f=[];function p(){const t=e[c+1];if(5===u&&"'"===t||6===u&&'"'===t)return c++,a="\\"+t,f[0](),!0}for(f[0]=()=>{void 0===n?n=a:n+=a},f[1]=()=>{void 0!==n&&(t.push(n),n=void 0)},f[2]=()=>{f[0](),m++},f[3]=()=>{if(m>0)m--,u=4,f[0]();else{if(m=0,void 0===n)return!1;if(n=formatSubPath(n),!1===n)return!1;f[1]()}};null!==u;)if(c++,r=e[c],"\\"!==r||!p()){if(o=getPathCharType(r),i=pathStateMachine[u],s=i[o]||i.l||8,8===s)return;if(u=s[0],void 0!==s[1]&&(l=f[s[1]],l&&(a=r,!1===l())))return;if(7===u)return t}}const cache=new Map;function resolveWithKeyValue(e,t){return isObject(e)?e[t]:null}function resolveValue(e,t){if(!isObject(e))return null;let r=cache.get(t);if(r||(r=parse(t),r&&cache.set(t,r)),!r)return null;const n=r.length;let a=e,o=0;for(;o<n;){const e=r[o];if(AST_NODE_PROPS_KEYS.includes(e)&&isMessageAST(a))return null;const t=a[e];if(void 0===t)return null;if(isFunction(a))return null;a=t,o++}return a}const DEFAULT_MODIFIER=e=>e,DEFAULT_MESSAGE=e=>"",DEFAULT_MESSAGE_DATA_TYPE="text",DEFAULT_NORMALIZE=e=>0===e.length?"":join(e),DEFAULT_INTERPOLATE=toDisplayString;function pluralDefault(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function getPluralIndex(e){const t=isNumber(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(isNumber(e.named.count)||isNumber(e.named.n))?isNumber(e.named.count)?e.named.count:isNumber(e.named.n)?e.named.n:t:t}function normalizeNamed(e,t){t.count||(t.count=e),t.n||(t.n=e)}function createMessageContext(e={}){const t=e.locale,r=getPluralIndex(e),n=isObject(e.pluralRules)&&isString(t)&&isFunction(e.pluralRules[t])?e.pluralRules[t]:pluralDefault,a=isObject(e.pluralRules)&&isString(t)&&isFunction(e.pluralRules[t])?pluralDefault:void 0,o=e.list||[],s=e.named||create();isNumber(e.pluralIndex)&&normalizeNamed(r,s);function l(t){const r=isFunction(e.messages)?e.messages(t):!!isObject(e.messages)&&e.messages[t];return r||(e.parent?e.parent.message(t):DEFAULT_MESSAGE)}const i=isPlainObject(e.processor)&&isFunction(e.processor.normalize)?e.processor.normalize:DEFAULT_NORMALIZE,c=isPlainObject(e.processor)&&isFunction(e.processor.interpolate)?e.processor.interpolate:DEFAULT_INTERPOLATE,u={list:e=>o[e],named:e=>s[e],plural:e=>e[n(r,e.length,a)],linked:(t,...r)=>{const[n,a]=r;let o="text",s="";1===r.length?isObject(n)?(s=n.modifier||s,o=n.type||o):isString(n)&&(s=n||s):2===r.length&&(isString(n)&&(s=n||s),isString(a)&&(o=a||o));const i=l(t)(u),c="vnode"===o&&isArray(i)&&s?i[0]:i;return s?(m=s,e.modifiers?e.modifiers[m]:DEFAULT_MODIFIER)(c,o):c;var m},message:l,type:isPlainObject(e.processor)&&isString(e.processor.type)?e.processor.type:DEFAULT_MESSAGE_DATA_TYPE,interpolate:c,normalize:i,values:assign(create(),o,s)};return u}const code$1=CompileErrorCodes.__EXTEND_POINT__,inc$1=incrementer(code$1),CoreErrorCodes={INVALID_ARGUMENT:code$1,INVALID_DATE_ARGUMENT:inc$1(),INVALID_ISO_DATE_ARGUMENT:inc$1(),NOT_SUPPORT_NON_STRING_MESSAGE:inc$1(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:inc$1(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:inc$1(),NOT_SUPPORT_LOCALE_TYPE:inc$1(),__EXTEND_POINT__:inc$1()};function getLocale(e,t){return null!=t.locale?resolveLocale(t.locale):resolveLocale(e.locale)}let _resolveLocale;function resolveLocale(e){if(isString(e))return e;if(isFunction(e)){if(e.resolvedOnce&&null!=_resolveLocale)return _resolveLocale;if("Function"===e.constructor.name){const t=e();if(isPromise(t))throw Error(CoreErrorCodes.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return _resolveLocale=t}throw Error(CoreErrorCodes.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw Error(CoreErrorCodes.NOT_SUPPORT_LOCALE_TYPE)}function fallbackWithSimple(e,t,r){return[...new Set([r,...isArray(t)?t:isObject(t)?Object.keys(t):isString(t)?[t]:[r]])]}function fallbackWithLocaleChain(e,t,r){const n=isString(r)?r:DEFAULT_LOCALE,a=e;a.__localeChainCache||(a.__localeChainCache=new Map);let o=a.__localeChainCache.get(n);if(!o){o=[];let e=[r];for(;isArray(e);)e=appendBlockToChain(o,e,t);const s=isArray(t)||!isPlainObject(t)?t:t.default?t.default:null;e=isString(s)?[s]:s,isArray(e)&&appendBlockToChain(o,e,!1),a.__localeChainCache.set(n,o)}return o}function appendBlockToChain(e,t,r){let n=!0;for(let a=0;a<t.length&&isBoolean(n);a++){const o=t[a];isString(o)&&(n=appendLocaleToChain(e,t[a],r))}return n}function appendLocaleToChain(e,t,r){let n;const a=t.split("-");do{n=appendItemToChain(e,a.join("-"),r),a.splice(-1,1)}while(a.length&&!0===n);return n}function appendItemToChain(e,t,r){let n=!1;if(!e.includes(t)&&(n=!0,t)){n="!"!==t[t.length-1];const a=t.replace(/!/g,"");e.push(a),(isArray(r)||isPlainObject(r))&&r[a]&&(n=r[a])}return n}const VERSION$1="9.14.5",NOT_REOSLVED=-1,DEFAULT_LOCALE="en-US",MISSING_RESOLVE_VALUE="",capitalize=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function getDefaultLinkedModifiers(){return{upper:(e,t)=>"text"===t&&isString(e)?e.toUpperCase():"vnode"===t&&isObject(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&isString(e)?e.toLowerCase():"vnode"===t&&isObject(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&isString(e)?capitalize(e):"vnode"===t&&isObject(e)&&"__v_isVNode"in e?capitalize(e.children):e}}let _compiler,_resolver,_fallbacker;function registerMessageCompiler(e){_compiler=e}function registerMessageResolver(e){_resolver=e}function registerLocaleFallbacker(e){_fallbacker=e}const setAdditionalMeta=e=>{};let _fallbackContext=null;const setFallbackContext=e=>{_fallbackContext=e},getFallbackContext=()=>_fallbackContext;let _cid=0;function createCoreContext(e={}){const t=isFunction(e.onWarn)?e.onWarn:warn,r=isString(e.version)?e.version:VERSION$1,n=isString(e.locale)||isFunction(e.locale)?e.locale:DEFAULT_LOCALE,a=isFunction(n)?DEFAULT_LOCALE:n,o=isArray(e.fallbackLocale)||isPlainObject(e.fallbackLocale)||isString(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:a,s=isPlainObject(e.messages)?e.messages:createResources(a),l=isPlainObject(e.datetimeFormats)?e.datetimeFormats:createResources(a),i=isPlainObject(e.numberFormats)?e.numberFormats:createResources(a),c=assign(create(),e.modifiers,getDefaultLinkedModifiers()),u=e.pluralRules||create(),m=isFunction(e.missing)?e.missing:null,f=!isBoolean(e.missingWarn)&&!isRegExp(e.missingWarn)||e.missingWarn,p=!isBoolean(e.fallbackWarn)&&!isRegExp(e.fallbackWarn)||e.fallbackWarn,_=!!e.fallbackFormat,g=!!e.unresolving,d=isFunction(e.postTranslation)?e.postTranslation:null,E=isPlainObject(e.processor)?e.processor:null,b=!isBoolean(e.warnHtmlMessage)||e.warnHtmlMessage,C=!!e.escapeParameter,O=isFunction(e.messageCompiler)?e.messageCompiler:_compiler,T=isFunction(e.messageResolver)?e.messageResolver:_resolver||resolveWithKeyValue,L=isFunction(e.localeFallbacker)?e.localeFallbacker:_fallbacker||fallbackWithSimple,N=isObject(e.fallbackContext)?e.fallbackContext:void 0,S=e,v=isObject(S.__datetimeFormatters)?S.__datetimeFormatters:new Map,h=isObject(S.__numberFormatters)?S.__numberFormatters:new Map,k=isObject(S.__meta)?S.__meta:{};_cid++;const P={version:r,cid:_cid,locale:n,fallbackLocale:o,messages:s,modifiers:c,pluralRules:u,missing:m,missingWarn:f,fallbackWarn:p,fallbackFormat:_,unresolving:g,postTranslation:d,processor:E,warnHtmlMessage:b,escapeParameter:C,messageCompiler:O,messageResolver:T,localeFallbacker:L,fallbackContext:N,onWarn:t,__meta:k};return P.datetimeFormats=l,P.numberFormats=i,P.__datetimeFormatters=v,P.__numberFormatters=h,P}const createResources=e=>({[e]:create()});function handleMissing(e,t,r,n,a){const{missing:o,onWarn:s}=e;if(null!==o){const n=o(e,r,t,a);return isString(n)?n:t}return t}function updateFallbackLocale(e,t,r){e.__localeChainCache=new Map,e.localeFallbacker(e,r,t)}function isAlmostSameLocale(e,t){return e!==t&&e.split("-")[0]===t.split("-")[0]}function isImplicitFallback(e,t){const r=t.indexOf(e);if(-1===r)return!1;for(let n=r+1;n<t.length;n++)if(isAlmostSameLocale(e,t[n]))return!0;return!1}function format(e){return t=>formatParts(t,e)}function formatParts(e,t){const r=resolveBody(t);if(null==r)throw createUnhandleNodeError(0);if(1===resolveType(r)){const t=resolveCases(r);return e.plural(t.reduce(((t,r)=>[...t,formatMessageParts(e,r)]),[]))}return formatMessageParts(e,r)}function formatMessageParts(e,t){const r=resolveStatic(t);if(null!=r)return"text"===e.type?r:e.normalize([r]);{const r=resolveItems(t).reduce(((t,r)=>[...t,formatMessagePart(e,r)]),[]);return e.normalize(r)}}function formatMessagePart(e,t){const r=resolveType(t);switch(r){case 3:case 9:case 7:case 8:return resolveValue$1(t,r);case 4:{const n=t;if(hasOwn(n,"k")&&n.k)return e.interpolate(e.named(n.k));if(hasOwn(n,"key")&&n.key)return e.interpolate(e.named(n.key));throw createUnhandleNodeError(r)}case 5:{const n=t;if(hasOwn(n,"i")&&isNumber(n.i))return e.interpolate(e.list(n.i));if(hasOwn(n,"index")&&isNumber(n.index))return e.interpolate(e.list(n.index));throw createUnhandleNodeError(r)}case 6:{const r=t,n=resolveLinkedModifier(r),a=resolveLinkedKey(r);return e.linked(formatMessagePart(e,a),n?formatMessagePart(e,n):void 0,e.type)}default:throw new Error(`unhandled node on format message part: ${r}`)}}const defaultOnCacheKey=e=>e;let compileCache=create();function baseCompile(e,t={}){let r=!1;const n=t.onError||defaultOnError;return t.onError=e=>{r=!0,n(e)},{...baseCompile$1(e,t),detectError:r}}function compile(e,t){if(isString(e)){!isBoolean(t.warnHtmlMessage)||t.warnHtmlMessage;const r=(t.onCacheKey||defaultOnCacheKey)(e),n=compileCache[r];if(n)return n;const{ast:a,detectError:o}=baseCompile(e,{...t,location:!1,jit:!0}),s=format(a);return o?s:compileCache[r]=s}{const t=e.cacheKey;if(t){const r=compileCache[t];return r||(compileCache[t]=format(e))}return format(e)}}const NOOP_MESSAGE_FUNCTION=()=>"",isMessageFunction=e=>isFunction(e);function translate(e,...t){const{fallbackFormat:r,postTranslation:n,unresolving:a,messageCompiler:o,fallbackLocale:s,messages:l}=e,[i,c]=parseTranslateArgs(...t),u=isBoolean(c.missingWarn)?c.missingWarn:e.missingWarn,m=isBoolean(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn,f=isBoolean(c.escapeParameter)?c.escapeParameter:e.escapeParameter,p=!!c.resolvedMessage,_=isString(c.default)||isBoolean(c.default)?isBoolean(c.default)?o?i:()=>i:c.default:r?o?i:()=>i:"",g=r||""!==_,d=getLocale(e,c);f&&escapeParams(c);let[E,b,C]=p?[i,d,l[d]||create()]:resolveMessageFormat(e,i,d,s,m,u),O=E,T=i;if(p||isString(O)||isMessageAST(O)||isMessageFunction(O)||g&&(O=_,T=O),!(p||(isString(O)||isMessageAST(O)||isMessageFunction(O))&&isString(b)))return a?NOT_REOSLVED:i;let L=!1;const N=isMessageFunction(O)?O:compileMessageFormat(e,i,b,O,T,(()=>{L=!0}));if(L)return O;const S=evaluateMessage(e,N,createMessageContext(getMessageContextOptions(e,b,C,c)));let v=n?n(S,i):S;return f&&isString(v)&&(v=sanitizeTranslatedHtml(v)),v}function escapeParams(e){isArray(e.list)?e.list=e.list.map((e=>isString(e)?escapeHtml(e):e)):isObject(e.named)&&Object.keys(e.named).forEach((t=>{isString(e.named[t])&&(e.named[t]=escapeHtml(e.named[t]))}))}function resolveMessageFormat(e,t,r,n,a,o){const{messages:s,onWarn:l,messageResolver:i,localeFallbacker:c}=e,u=c(e,n,r);let m,f=create(),p=null;for(let _=0;_<u.length&&(m=u[_],f=s[m]||create(),null===(p=i(f,t))&&(p=f[t]),!(isString(p)||isMessageAST(p)||isMessageFunction(p)));_++)if(!isImplicitFallback(m,u)){const r=handleMissing(e,t,m,o,"translate");r!==t&&(p=r)}return[p,m,f]}function compileMessageFormat(e,t,r,n,a,o){const{messageCompiler:s,warnHtmlMessage:l}=e;if(isMessageFunction(n)){const e=n;return e.locale=e.locale||r,e.key=e.key||t,e}if(null==s){const e=()=>n;return e.locale=r,e.key=t,e}const i=s(n,getCompileContext(e,r,a,n,l,o));return i.locale=r,i.key=t,i.source=n,i}function evaluateMessage(e,t,r){return t(r)}function parseTranslateArgs(...e){const[t,r,n]=e,a=create();if(!(isString(t)||isNumber(t)||isMessageFunction(t)||isMessageAST(t)))throw Error(CoreErrorCodes.INVALID_ARGUMENT);const o=isNumber(t)?String(t):(isMessageFunction(t),t);return isNumber(r)?a.plural=r:isString(r)?a.default=r:isPlainObject(r)&&!isEmptyObject(r)?a.named=r:isArray(r)&&(a.list=r),isNumber(n)?a.plural=n:isString(n)?a.default=n:isPlainObject(n)&&assign(a,n),[o,a]}function getCompileContext(e,t,r,n,a,o){return{locale:t,key:r,warnHtmlMessage:a,onError:e=>{throw o&&o(e),e},onCacheKey:e=>generateFormatCacheKey(t,r,e)}}function getMessageContextOptions(e,t,r,n){const{modifiers:a,pluralRules:o,messageResolver:s,fallbackLocale:l,fallbackWarn:i,missingWarn:c,fallbackContext:u}=e,m={locale:t,modifiers:a,pluralRules:o,messages:n=>{let a=s(r,n);if(null==a&&u){const[,,e]=resolveMessageFormat(u,n,t,l,i,c);a=s(e,n)}if(isString(a)||isMessageAST(a)){let r=!1;const o=compileMessageFormat(e,n,t,a,n,(()=>{r=!0}));return r?NOOP_MESSAGE_FUNCTION:o}return isMessageFunction(a)?a:NOOP_MESSAGE_FUNCTION}};return e.processor&&(m.processor=e.processor),n.list&&(m.list=n.list),n.named&&(m.named=n.named),isNumber(n.plural)&&(m.pluralIndex=n.plural),m}function datetime(e,...t){const{datetimeFormats:r,unresolving:n,fallbackLocale:a,onWarn:o,localeFallbacker:s}=e,{__datetimeFormatters:l}=e,[i,c,u,m]=parseDateTimeArgs(...t),f=isBoolean(u.missingWarn)?u.missingWarn:e.missingWarn;isBoolean(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const p=!!u.part,_=getLocale(e,u),g=s(e,a,_);if(!isString(i)||""===i)return new Intl.DateTimeFormat(_,m).format(c);let d,E={},b=null;for(let T=0;T<g.length&&(d=g[T],E=r[d]||{},b=E[i],!isPlainObject(b));T++)handleMissing(e,i,d,f,"datetime format");if(!isPlainObject(b)||!isString(d))return n?NOT_REOSLVED:i;let C=`${d}__${i}`;isEmptyObject(m)||(C=`${C}__${JSON.stringify(m)}`);let O=l.get(C);return O||(O=new Intl.DateTimeFormat(d,assign({},b,m)),l.set(C,O)),p?O.formatToParts(c):O.format(c)}const DATETIME_FORMAT_OPTIONS_KEYS=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function parseDateTimeArgs(...e){const[t,r,n,a]=e,o=create();let s,l=create();if(isString(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT);const r=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();s=new Date(r);try{s.toISOString()}catch(i){throw Error(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT)}}else if(isDate(t)){if(isNaN(t.getTime()))throw Error(CoreErrorCodes.INVALID_DATE_ARGUMENT);s=t}else{if(!isNumber(t))throw Error(CoreErrorCodes.INVALID_ARGUMENT);s=t}return isString(r)?o.key=r:isPlainObject(r)&&Object.keys(r).forEach((e=>{DATETIME_FORMAT_OPTIONS_KEYS.includes(e)?l[e]=r[e]:o[e]=r[e]})),isString(n)?o.locale=n:isPlainObject(n)&&(l=n),isPlainObject(a)&&(l=a),[o.key||"",s,o,l]}function clearDateTimeFormat(e,t,r){const n=e;for(const a in r){const e=`${t}__${a}`;n.__datetimeFormatters.has(e)&&n.__datetimeFormatters.delete(e)}}function number(e,...t){const{numberFormats:r,unresolving:n,fallbackLocale:a,onWarn:o,localeFallbacker:s}=e,{__numberFormatters:l}=e,[i,c,u,m]=parseNumberArgs(...t),f=isBoolean(u.missingWarn)?u.missingWarn:e.missingWarn;isBoolean(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const p=!!u.part,_=getLocale(e,u),g=s(e,a,_);if(!isString(i)||""===i)return new Intl.NumberFormat(_,m).format(c);let d,E={},b=null;for(let T=0;T<g.length&&(d=g[T],E=r[d]||{},b=E[i],!isPlainObject(b));T++)handleMissing(e,i,d,f,"number format");if(!isPlainObject(b)||!isString(d))return n?NOT_REOSLVED:i;let C=`${d}__${i}`;isEmptyObject(m)||(C=`${C}__${JSON.stringify(m)}`);let O=l.get(C);return O||(O=new Intl.NumberFormat(d,assign({},b,m)),l.set(C,O)),p?O.formatToParts(c):O.format(c)}const NUMBER_FORMAT_OPTIONS_KEYS=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function parseNumberArgs(...e){const[t,r,n,a]=e,o=create();let s=create();if(!isNumber(t))throw Error(CoreErrorCodes.INVALID_ARGUMENT);const l=t;return isString(r)?o.key=r:isPlainObject(r)&&Object.keys(r).forEach((e=>{NUMBER_FORMAT_OPTIONS_KEYS.includes(e)?s[e]=r[e]:o[e]=r[e]})),isString(n)?o.locale=n:isPlainObject(n)&&(s=n),isPlainObject(a)&&(s=a),[o.key||"",l,o,s]}function clearNumberFormat(e,t,r){const n=e;for(const a in r){const e=`${t}__${a}`;n.__numberFormatters.has(e)&&n.__numberFormatters.delete(e)}}const VERSION="9.14.5",code=CoreErrorCodes.__EXTEND_POINT__,inc=incrementer(code),I18nErrorCodes={UNEXPECTED_RETURN_TYPE:code,INVALID_ARGUMENT:inc(),MUST_BE_CALL_SETUP_TOP:inc(),NOT_INSTALLED:inc(),NOT_AVAILABLE_IN_LEGACY_MODE:inc(),REQUIRED_VALUE:inc(),INVALID_VALUE:inc(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:inc(),NOT_INSTALLED_WITH_PROVIDE:inc(),UNEXPECTED_ERROR:inc(),NOT_COMPATIBLE_LEGACY_VUE_I18N:inc(),BRIDGE_SUPPORT_VUE_2_ONLY:inc(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:inc(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:inc(),__EXTEND_POINT__:inc()};function createI18nError(e,...t){return createCompileError(e,null,void 0)}const TranslateVNodeSymbol=makeSymbol("__translateVNode"),DatetimePartsSymbol=makeSymbol("__datetimeParts"),NumberPartsSymbol=makeSymbol("__numberParts"),SetPluralRulesSymbol=makeSymbol("__setPluralRules"),InejctWithOptionSymbol=makeSymbol("__injectWithOption"),DisposeSymbol=makeSymbol("__dispose"),__VUE_I18N_BRIDGE__="__VUE_I18N_BRIDGE__";function handleFlatJson(e){if(!isObject(e))return e;if(isMessageAST(e))return e;for(const t in e)if(hasOwn(e,t))if(t.includes(".")){const r=t.split("."),n=r.length-1;let a=e,o=!1;for(let e=0;e<n;e++){if("__proto__"===r[e])throw new Error(`unsafe key: ${r[e]}`);if(r[e]in a||(a[r[e]]=create()),!isObject(a[r[e]])){o=!0;break}a=a[r[e]]}if(o||(isMessageAST(a)?AST_NODE_PROPS_KEYS.includes(r[n])||delete e[t]:(a[r[n]]=e[t],delete e[t])),!isMessageAST(a)){const e=a[r[n]];isObject(e)&&handleFlatJson(e)}}else isObject(e[t])&&handleFlatJson(e[t]);return e}function getLocaleMessages(e,t){const{messages:r,__i18n:n,messageResolver:a,flatJson:o}=t,s=isPlainObject(r)?r:isArray(n)?create():{[e]:create()};if(isArray(n)&&n.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:r}=e;t?(s[t]=s[t]||create(),deepCopy(r,s[t])):deepCopy(r,s)}else isString(e)&&deepCopy(JSON.parse(e),s)})),null==a&&o)for(const l in s)hasOwn(s,l)&&handleFlatJson(s[l]);return s}function getComponentOptions(e){return e.type}function adjustI18nResources(e,t,r){let n=isObject(t.messages)?t.messages:create();"__i18nGlobal"in r&&(n=getLocaleMessages(e.locale.value,{messages:n,__i18n:r.__i18nGlobal}));const a=Object.keys(n);if(a.length&&a.forEach((t=>{e.mergeLocaleMessage(t,n[t])})),isObject(t.datetimeFormats)){const r=Object.keys(t.datetimeFormats);r.length&&r.forEach((r=>{e.mergeDateTimeFormat(r,t.datetimeFormats[r])}))}if(isObject(t.numberFormats)){const r=Object.keys(t.numberFormats);r.length&&r.forEach((r=>{e.mergeNumberFormat(r,t.numberFormats[r])}))}}function createTextNode(e){return createVNode(Text,null,e,0)}const DEVTOOLS_META="__INTLIFY_META__",NOOP_RETURN_ARRAY=()=>[],NOOP_RETURN_FALSE=()=>!1;let composerID=0;function defineCoreMissingHandler(e){return(t,r,n,a)=>e(r,n,getCurrentInstance()||void 0,a)}const getMetaInfo=()=>{const e=getCurrentInstance();let t=null;return e&&(t=getComponentOptions(e)[DEVTOOLS_META])?{[DEVTOOLS_META]:t}:null};function createComposer(e={},t){const{__root:r,__injectWithOption:n}=e,a=void 0===r,o=e.flatJson,s=inBrowser?ref:shallowRef,l=!!e.translateExistCompatible;let i=!isBoolean(e.inheritLocale)||e.inheritLocale;const c=s(r&&i?r.locale.value:isString(e.locale)?e.locale:DEFAULT_LOCALE),u=s(r&&i?r.fallbackLocale.value:isString(e.fallbackLocale)||isArray(e.fallbackLocale)||isPlainObject(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:c.value),m=s(getLocaleMessages(c.value,e)),f=s(isPlainObject(e.datetimeFormats)?e.datetimeFormats:{[c.value]:{}}),p=s(isPlainObject(e.numberFormats)?e.numberFormats:{[c.value]:{}});let _=r?r.missingWarn:!isBoolean(e.missingWarn)&&!isRegExp(e.missingWarn)||e.missingWarn,g=r?r.fallbackWarn:!isBoolean(e.fallbackWarn)&&!isRegExp(e.fallbackWarn)||e.fallbackWarn,d=r?r.fallbackRoot:!isBoolean(e.fallbackRoot)||e.fallbackRoot,E=!!e.fallbackFormat,b=isFunction(e.missing)?e.missing:null,C=isFunction(e.missing)?defineCoreMissingHandler(e.missing):null,O=isFunction(e.postTranslation)?e.postTranslation:null,T=r?r.warnHtmlMessage:!isBoolean(e.warnHtmlMessage)||e.warnHtmlMessage,L=!!e.escapeParameter;const N=r?r.modifiers:isPlainObject(e.modifiers)?e.modifiers:{};let S,v=e.pluralRules||r&&r.pluralRules;S=(()=>{a&&setFallbackContext(null);const t={version:VERSION,locale:c.value,fallbackLocale:u.value,messages:m.value,modifiers:N,pluralRules:v,missing:null===C?void 0:C,missingWarn:_,fallbackWarn:g,fallbackFormat:E,unresolving:!0,postTranslation:null===O?void 0:O,warnHtmlMessage:T,escapeParameter:L,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};t.datetimeFormats=f.value,t.numberFormats=p.value,t.__datetimeFormatters=isPlainObject(S)?S.__datetimeFormatters:void 0,t.__numberFormatters=isPlainObject(S)?S.__numberFormatters:void 0;const r=createCoreContext(t);return a&&setFallbackContext(r),r})(),updateFallbackLocale(S,c.value,u.value);const h=computed({get:()=>c.value,set:e=>{c.value=e,S.locale=c.value}}),k=computed({get:()=>u.value,set:e=>{u.value=e,S.fallbackLocale=u.value,updateFallbackLocale(S,c.value,e)}}),P=computed((()=>m.value)),I=computed((()=>f.value)),A=computed((()=>p.value));const y=(e,t,n,o,s,l)=>{let i;c.value,u.value,m.value,f.value,p.value;try{0,a||(S.fallbackContext=r?getFallbackContext():void 0),i=e(S)}finally{a||(S.fallbackContext=void 0)}if("translate exists"!==n&&isNumber(i)&&i===NOT_REOSLVED||"translate exists"===n&&!i){const[e,n]=t();return r&&d?o(r):s(e)}if(l(i))return i;throw Error(I18nErrorCodes.UNEXPECTED_RETURN_TYPE)};function F(...e){return y((t=>Reflect.apply(translate,null,[t,...e])),(()=>parseTranslateArgs(...e)),"translate",(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>isString(e)))}const R={normalize:function(e){return e.map((e=>isString(e)||isNumber(e)||isBoolean(e)?createTextNode(String(e)):e))},interpolate:e=>e,type:"vnode"};function M(e){return m.value[e]||{}}composerID++,r&&inBrowser&&(watch(r.locale,(e=>{i&&(c.value=e,S.locale=e,updateFallbackLocale(S,c.value,u.value))})),watch(r.fallbackLocale,(e=>{i&&(u.value=e,S.fallbackLocale=e,updateFallbackLocale(S,c.value,u.value))})));const D={id:composerID,locale:h,fallbackLocale:k,get inheritLocale(){return i},set inheritLocale(e){i=e,e&&r&&(c.value=r.locale.value,u.value=r.fallbackLocale.value,updateFallbackLocale(S,c.value,u.value))},get availableLocales(){return Object.keys(m.value).sort()},messages:P,get modifiers(){return N},get pluralRules(){return v||{}},get isGlobal(){return a},get missingWarn(){return _},set missingWarn(e){_=e,S.missingWarn=_},get fallbackWarn(){return g},set fallbackWarn(e){g=e,S.fallbackWarn=g},get fallbackRoot(){return d},set fallbackRoot(e){d=e},get fallbackFormat(){return E},set fallbackFormat(e){E=e,S.fallbackFormat=E},get warnHtmlMessage(){return T},set warnHtmlMessage(e){T=e,S.warnHtmlMessage=e},get escapeParameter(){return L},set escapeParameter(e){L=e,S.escapeParameter=e},t:F,getLocaleMessage:M,setLocaleMessage:function(e,t){if(o){const r={[e]:t};for(const e in r)hasOwn(r,e)&&handleFlatJson(r[e]);t=r[e]}m.value[e]=t,S.messages=m.value},mergeLocaleMessage:function(e,t){m.value[e]=m.value[e]||{};const r={[e]:t};if(o)for(const n in r)hasOwn(r,n)&&handleFlatJson(r[n]);deepCopy(t=r[e],m.value[e]),S.messages=m.value},getPostTranslationHandler:function(){return isFunction(O)?O:null},setPostTranslationHandler:function(e){O=e,S.postTranslation=e},getMissingHandler:function(){return b},setMissingHandler:function(e){null!==e&&(C=defineCoreMissingHandler(e)),b=e,S.missing=C},[SetPluralRulesSymbol]:function(e){v=e,S.pluralRules=v}};return D.datetimeFormats=I,D.numberFormats=A,D.rt=function(...e){const[t,r,n]=e;if(n&&!isObject(n))throw Error(I18nErrorCodes.INVALID_ARGUMENT);return F(t,r,assign({resolvedMessage:!0},n||{}))},D.te=function(e,t){return y((()=>{if(!e)return!1;const r=M(isString(t)?t:c.value),n=S.messageResolver(r,e);return l?null!=n:isMessageAST(n)||isMessageFunction(n)||isString(n)}),(()=>[e]),"translate exists",(r=>Reflect.apply(r.te,r,[e,t])),NOOP_RETURN_FALSE,(e=>isBoolean(e)))},D.tm=function(e){const t=function(e){let t=null;const r=fallbackWithLocaleChain(S,u.value,c.value);for(let n=0;n<r.length;n++){const a=m.value[r[n]]||{},o=S.messageResolver(a,e);if(null!=o){t=o;break}}return t}(e);return null!=t?t:r&&r.tm(e)||{}},D.d=function(...e){return y((t=>Reflect.apply(datetime,null,[t,...e])),(()=>parseDateTimeArgs(...e)),"datetime format",(t=>Reflect.apply(t.d,t,[...e])),(()=>MISSING_RESOLVE_VALUE),(e=>isString(e)))},D.n=function(...e){return y((t=>Reflect.apply(number,null,[t,...e])),(()=>parseNumberArgs(...e)),"number format",(t=>Reflect.apply(t.n,t,[...e])),(()=>MISSING_RESOLVE_VALUE),(e=>isString(e)))},D.getDateTimeFormat=function(e){return f.value[e]||{}},D.setDateTimeFormat=function(e,t){f.value[e]=t,S.datetimeFormats=f.value,clearDateTimeFormat(S,e,t)},D.mergeDateTimeFormat=function(e,t){f.value[e]=assign(f.value[e]||{},t),S.datetimeFormats=f.value,clearDateTimeFormat(S,e,t)},D.getNumberFormat=function(e){return p.value[e]||{}},D.setNumberFormat=function(e,t){p.value[e]=t,S.numberFormats=p.value,clearNumberFormat(S,e,t)},D.mergeNumberFormat=function(e,t){p.value[e]=assign(p.value[e]||{},t),S.numberFormats=p.value,clearNumberFormat(S,e,t)},D[InejctWithOptionSymbol]=n,D[TranslateVNodeSymbol]=function(...e){return y((t=>{let r;const n=t;try{n.processor=R,r=Reflect.apply(translate,null,[n,...e])}finally{n.processor=null}return r}),(()=>parseTranslateArgs(...e)),"translate",(t=>t[TranslateVNodeSymbol](...e)),(e=>[createTextNode(e)]),(e=>isArray(e)))},D[DatetimePartsSymbol]=function(...e){return y((t=>Reflect.apply(datetime,null,[t,...e])),(()=>parseDateTimeArgs(...e)),"datetime format",(t=>t[DatetimePartsSymbol](...e)),NOOP_RETURN_ARRAY,(e=>isString(e)||isArray(e)))},D[NumberPartsSymbol]=function(...e){return y((t=>Reflect.apply(number,null,[t,...e])),(()=>parseNumberArgs(...e)),"number format",(t=>t[NumberPartsSymbol](...e)),NOOP_RETURN_ARRAY,(e=>isString(e)||isArray(e)))},D}function convertComposerOptions(e){const t=isString(e.locale)?e.locale:DEFAULT_LOCALE,r=isString(e.fallbackLocale)||isArray(e.fallbackLocale)||isPlainObject(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,n=isFunction(e.missing)?e.missing:void 0,a=!isBoolean(e.silentTranslationWarn)&&!isRegExp(e.silentTranslationWarn)||!e.silentTranslationWarn,o=!isBoolean(e.silentFallbackWarn)&&!isRegExp(e.silentFallbackWarn)||!e.silentFallbackWarn,s=!isBoolean(e.fallbackRoot)||e.fallbackRoot,l=!!e.formatFallbackMessages,i=isPlainObject(e.modifiers)?e.modifiers:{},c=e.pluralizationRules,u=isFunction(e.postTranslation)?e.postTranslation:void 0,m=!isString(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,f=!!e.escapeParameterHtml,p=!isBoolean(e.sync)||e.sync;let _=e.messages;if(isPlainObject(e.sharedMessages)){const t=e.sharedMessages;_=Object.keys(t).reduce(((e,r)=>{const n=e[r]||(e[r]={});return assign(n,t[r]),e}),_||{})}const{__i18n:g,__root:d,__injectWithOption:E}=e,b=e.datetimeFormats,C=e.numberFormats,O=e.flatJson,T=e.translateExistCompatible;return{locale:t,fallbackLocale:r,messages:_,flatJson:O,datetimeFormats:b,numberFormats:C,missing:n,missingWarn:a,fallbackWarn:o,fallbackRoot:s,fallbackFormat:l,modifiers:i,pluralRules:c,postTranslation:u,warnHtmlMessage:m,escapeParameter:f,messageResolver:e.messageResolver,inheritLocale:p,translateExistCompatible:T,__i18n:g,__root:d,__injectWithOption:E}}function createVueI18n(e={},t){{const t=createComposer(convertComposerOptions(e)),{__extender:r}=e,n={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get formatter(){return{interpolate:()=>[]}},set formatter(e){},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return isBoolean(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=isBoolean(e)?!e:e},get silentFallbackWarn(){return isBoolean(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=isBoolean(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(e){},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...e){const[r,n,a]=e,o={};let s=null,l=null;if(!isString(r))throw Error(I18nErrorCodes.INVALID_ARGUMENT);const i=r;return isString(n)?o.locale=n:isArray(n)?s=n:isPlainObject(n)&&(l=n),isArray(a)?s=a:isPlainObject(a)&&(l=a),Reflect.apply(t.t,t,[i,s||l||{},o])},rt:(...e)=>Reflect.apply(t.rt,t,[...e]),tc(...e){const[r,n,a]=e,o={plural:1};let s=null,l=null;if(!isString(r))throw Error(I18nErrorCodes.INVALID_ARGUMENT);const i=r;return isString(n)?o.locale=n:isNumber(n)?o.plural=n:isArray(n)?s=n:isPlainObject(n)&&(l=n),isString(a)?o.locale=a:isArray(a)?s=a:isPlainObject(a)&&(l=a),Reflect.apply(t.t,t,[i,s||l||{},o])},te:(e,r)=>t.te(e,r),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,r){t.setLocaleMessage(e,r)},mergeLocaleMessage(e,r){t.mergeLocaleMessage(e,r)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,r){t.setDateTimeFormat(e,r)},mergeDateTimeFormat(e,r){t.mergeDateTimeFormat(e,r)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,r){t.setNumberFormat(e,r)},mergeNumberFormat(e,r){t.mergeNumberFormat(e,r)},getChoiceIndex:(e,t)=>-1};return n.__extender=r,n}}const baseFormatProps={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function getInterpolateArg({slots:e},t){if(1===t.length&&"default"===t[0]){return(e.default?e.default():[]).reduce(((e,t)=>[...e,...t.type===Fragment?t.children:[t]]),[])}return t.reduce(((t,r)=>{const n=e[r];return n&&(t[r]=n()),t}),create())}function getFragmentableTag(e){return Fragment}const TranslationImpl=defineComponent({name:"i18n-t",props:assign({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>isNumber(e)||!isNaN(e)}},baseFormatProps),setup(e,t){const{slots:r,attrs:n}=t,a=e.i18n||useI18n({useScope:e.scope,__useComponent:!0});return()=>{const o=Object.keys(r).filter((e=>"_"!==e)),s=create();e.locale&&(s.locale=e.locale),void 0!==e.plural&&(s.plural=isString(e.plural)?+e.plural:e.plural);const l=getInterpolateArg(t,o),i=a[TranslateVNodeSymbol](e.keypath,l,s),c=assign(create(),n),u=isString(e.tag)||isObject(e.tag)?e.tag:getFragmentableTag();return h(u,c,i)}}}),Translation=TranslationImpl,I18nT=Translation;function isVNode(e){return isArray(e)&&!isString(e[0])}function renderFormatter(e,t,r,n){const{slots:a,attrs:o}=t;return()=>{const t={part:!0};let s=create();e.locale&&(t.locale=e.locale),isString(e.format)?t.key=e.format:isObject(e.format)&&(isString(e.format.key)&&(t.key=e.format.key),s=Object.keys(e.format).reduce(((t,n)=>r.includes(n)?assign(create(),t,{[n]:e.format[n]}):t),create()));const l=n(e.value,t,s);let i=[t.key];isArray(l)?i=l.map(((e,t)=>{const r=a[e.type],n=r?r({[e.type]:e.value,index:t,parts:l}):[e.value];return isVNode(n)&&(n[0].key=`${e.type}-${t}`),n})):isString(l)&&(i=[l]);const c=assign(create(),o),u=isString(e.tag)||isObject(e.tag)?e.tag:getFragmentableTag();return h(u,c,i)}}const NumberFormatImpl=defineComponent({name:"i18n-n",props:assign({value:{type:Number,required:!0},format:{type:[String,Object]}},baseFormatProps),setup(e,t){const r=e.i18n||useI18n({useScope:e.scope,__useComponent:!0});return renderFormatter(e,t,NUMBER_FORMAT_OPTIONS_KEYS,((...e)=>r[NumberPartsSymbol](...e)))}}),NumberFormat=NumberFormatImpl,I18nN=NumberFormat,DatetimeFormatImpl=defineComponent({name:"i18n-d",props:assign({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},baseFormatProps),setup(e,t){const r=e.i18n||useI18n({useScope:e.scope,__useComponent:!0});return renderFormatter(e,t,DATETIME_FORMAT_OPTIONS_KEYS,((...e)=>r[DatetimePartsSymbol](...e)))}}),DatetimeFormat=DatetimeFormatImpl,I18nD=DatetimeFormat;function getComposer$1(e,t){const r=e;if("composition"===e.mode)return r.__getInstance(t)||e.global;{const n=r.__getInstance(t);return null!=n?n.__composer:e.global.__composer}}function vTDirective(e){const t=t=>{const{instance:r,modifiers:n,value:a}=t;if(!r||!r.$)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);const o=getComposer$1(e,r.$),s=parseValue(a);return[Reflect.apply(o.t,o,[...makeParams(s)]),o]};return{created:(r,n)=>{const[a,o]=t(n);inBrowser&&e.global===o&&(r.__i18nWatcher=watch(o.locale,(()=>{n.instance&&n.instance.$forceUpdate()}))),r.__composer=o,r.textContent=a},unmounted:e=>{inBrowser&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const r=e.__composer,n=parseValue(t);e.textContent=Reflect.apply(r.t,r,[...makeParams(n)])}},getSSRProps:e=>{const[r]=t(e);return{textContent:r}}}}function parseValue(e){if(isString(e))return{path:e};if(isPlainObject(e)){if(!("path"in e))throw Error(I18nErrorCodes.REQUIRED_VALUE,"path");return e}throw Error(I18nErrorCodes.INVALID_VALUE)}function makeParams(e){const{path:t,locale:r,args:n,choice:a,plural:o}=e,s={},l=n||{};return isString(r)&&(s.locale=r),isNumber(a)&&(s.plural=a),isNumber(o)&&(s.plural=o),[t,l,s]}function apply(e,t,...r){const n=isPlainObject(r[0])?r[0]:{},a=!!n.useI18nComponentName;(!isBoolean(n.globalInstall)||n.globalInstall)&&([a?"i18n":Translation.name,"I18nT"].forEach((t=>e.component(t,Translation))),[NumberFormat.name,"I18nN"].forEach((t=>e.component(t,NumberFormat))),[DatetimeFormat.name,"I18nD"].forEach((t=>e.component(t,DatetimeFormat)))),e.directive("t",vTDirective(t))}function defineMixin(e,t,r){return{beforeCreate(){const n=getCurrentInstance();if(!n)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);const a=this.$options;if(a.i18n){const n=a.i18n;if(a.__i18n&&(n.__i18n=a.__i18n),n.__root=t,this===this.$root)this.$i18n=mergeToGlobal(e,n);else{n.__injectWithOption=!0,n.__extender=r.__vueI18nExtend,this.$i18n=createVueI18n(n);const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}}else if(a.__i18n)if(this===this.$root)this.$i18n=mergeToGlobal(e,a);else{this.$i18n=createVueI18n({__i18n:a.__i18n,__injectWithOption:!0,__extender:r.__vueI18nExtend,__root:t});const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}else this.$i18n=e;a.__i18nGlobal&&adjustI18nResources(t,a,a),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e),r.__setInstance(n,this.$i18n)},mounted(){},unmounted(){const e=getCurrentInstance();if(!e)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);const t=this.$i18n;delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,t.__disposer&&(t.__disposer(),delete t.__disposer,delete t.__extender),r.__deleteInstance(e),delete this.$i18n}}}function mergeToGlobal(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[SetPluralRulesSymbol](t.pluralizationRules||e.pluralizationRules);const r=getLocaleMessages(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(r).forEach((t=>e.mergeLocaleMessage(t,r[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((r=>e.mergeDateTimeFormat(r,t.datetimeFormats[r]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((r=>e.mergeNumberFormat(r,t.numberFormats[r]))),e}const I18nInjectionKey=makeSymbol("global-vue-i18n");function createI18n(e={},t){const r=!isBoolean(e.legacy)||e.legacy,n=!isBoolean(e.globalInjection)||e.globalInjection,a=!r||!!e.allowComposition,o=new Map,[s,l]=createGlobal(e,r),i=makeSymbol("");{const e={get mode(){return r?"legacy":"composition"},get allowComposition(){return a},async install(t,...a){if(t.__VUE_I18N_SYMBOL__=i,t.provide(t.__VUE_I18N_SYMBOL__,e),isPlainObject(a[0])){const t=a[0];e.__composerExtend=t.__composerExtend,e.__vueI18nExtend=t.__vueI18nExtend}let o=null;!r&&n&&(o=injectGlobalFields(t,e.global)),apply(t,e,...a),r&&t.mixin(defineMixin(l,l.__composer,e));const s=t.unmount;t.unmount=()=>{o&&o(),e.dispose(),s()}},get global(){return l},dispose(){s.stop()},__instances:o,__getInstance:function(e){return o.get(e)||null},__setInstance:function(e,t){o.set(e,t)},__deleteInstance:function(e){o.delete(e)}};return e}}function useI18n(e={}){const t=getCurrentInstance();if(null==t)throw Error(I18nErrorCodes.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&null!=t.appContext.app&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw Error(I18nErrorCodes.NOT_INSTALLED);const r=getI18nInstance(t),n=getGlobalComposer(r),a=getComponentOptions(t),o=getScope(e,a);if("legacy"===r.mode&&!e.__useComponent){if(!r.allowComposition)throw Error(I18nErrorCodes.NOT_AVAILABLE_IN_LEGACY_MODE);return useI18nForLegacy(t,o,n,e)}if("global"===o)return adjustI18nResources(n,e,a),n;if("parent"===o){let a=getComposer(r,t,e.__useComponent);return null==a&&(a=n),a}const s=r;let l=s.__getInstance(t);if(null==l){const r=assign({},e);"__i18n"in a&&(r.__i18n=a.__i18n),n&&(r.__root=n),l=createComposer(r),s.__composerExtend&&(l[DisposeSymbol]=s.__composerExtend(l)),setupLifeCycle(s,t,l),s.__setInstance(t,l)}return l}const castToVueI18n=e=>{if(!(__VUE_I18N_BRIDGE__ in e))throw Error(I18nErrorCodes.NOT_COMPATIBLE_LEGACY_VUE_I18N);return e};function createGlobal(e,t,r){const n=effectScope();{const r=t?n.run((()=>createVueI18n(e))):n.run((()=>createComposer(e)));if(null==r)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);return[n,r]}}function getI18nInstance(e){{const t=inject(e.isCE?I18nInjectionKey:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw createI18nError(e.isCE?I18nErrorCodes.NOT_INSTALLED_WITH_PROVIDE:I18nErrorCodes.UNEXPECTED_ERROR);return t}}function getScope(e,t){return isEmptyObject(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function getGlobalComposer(e){return"composition"===e.mode?e.global:e.global.__composer}function getComposer(e,t,r=!1){let n=null;const a=t.root;let o=getParentComponentInstance(t,r);for(;null!=o;){const t=e;if("composition"===e.mode)n=t.__getInstance(o);else{const e=t.__getInstance(o);null!=e&&(n=e.__composer,r&&n&&!n[InejctWithOptionSymbol]&&(n=null))}if(null!=n)break;if(a===o)break;o=o.parent}return n}function getParentComponentInstance(e,t=!1){return null==e?null:t&&e.vnode.ctx||e.parent}function setupLifeCycle(e,t,r){onMounted((()=>{}),t),onUnmounted((()=>{const n=r;e.__deleteInstance(t);const a=n[DisposeSymbol];a&&(a(),delete n[DisposeSymbol])}),t)}function useI18nForLegacy(e,t,r,n={}){const a="local"===t,o=shallowRef(null);if(a&&e.proxy&&!e.proxy.$options.i18n&&!e.proxy.$options.__i18n)throw Error(I18nErrorCodes.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);const s=isBoolean(n.inheritLocale)?n.inheritLocale:!isString(n.locale),l=ref(!a||s?r.locale.value:isString(n.locale)?n.locale:DEFAULT_LOCALE),i=ref(!a||s?r.fallbackLocale.value:isString(n.fallbackLocale)||isArray(n.fallbackLocale)||isPlainObject(n.fallbackLocale)||!1===n.fallbackLocale?n.fallbackLocale:l.value),c=ref(getLocaleMessages(l.value,n)),u=ref(isPlainObject(n.datetimeFormats)?n.datetimeFormats:{[l.value]:{}}),m=ref(isPlainObject(n.numberFormats)?n.numberFormats:{[l.value]:{}}),f=a?r.missingWarn:!isBoolean(n.missingWarn)&&!isRegExp(n.missingWarn)||n.missingWarn,p=a?r.fallbackWarn:!isBoolean(n.fallbackWarn)&&!isRegExp(n.fallbackWarn)||n.fallbackWarn,_=a?r.fallbackRoot:!isBoolean(n.fallbackRoot)||n.fallbackRoot,g=!!n.fallbackFormat,d=isFunction(n.missing)?n.missing:null,E=isFunction(n.postTranslation)?n.postTranslation:null,b=a?r.warnHtmlMessage:!isBoolean(n.warnHtmlMessage)||n.warnHtmlMessage,C=!!n.escapeParameter,O=a?r.modifiers:isPlainObject(n.modifiers)?n.modifiers:{},T=n.pluralRules||a&&r.pluralRules;function L(e){return l.value,i.value,c.value,u.value,m.value,e()}const N={get id(){return o.value?o.value.id:-1},locale:computed({get:()=>o.value?o.value.locale.value:l.value,set:e=>{o.value&&(o.value.locale.value=e),l.value=e}}),fallbackLocale:computed({get:()=>o.value?o.value.fallbackLocale.value:i.value,set:e=>{o.value&&(o.value.fallbackLocale.value=e),i.value=e}}),messages:computed((()=>o.value?o.value.messages.value:c.value)),datetimeFormats:computed((()=>u.value)),numberFormats:computed((()=>m.value)),get inheritLocale(){return o.value?o.value.inheritLocale:s},set inheritLocale(e){o.value&&(o.value.inheritLocale=e)},get availableLocales(){return o.value?o.value.availableLocales:Object.keys(c.value)},get modifiers(){return o.value?o.value.modifiers:O},get pluralRules(){return o.value?o.value.pluralRules:T},get isGlobal(){return!!o.value&&o.value.isGlobal},get missingWarn(){return o.value?o.value.missingWarn:f},set missingWarn(e){o.value&&(o.value.missingWarn=e)},get fallbackWarn(){return o.value?o.value.fallbackWarn:p},set fallbackWarn(e){o.value&&(o.value.missingWarn=e)},get fallbackRoot(){return o.value?o.value.fallbackRoot:_},set fallbackRoot(e){o.value&&(o.value.fallbackRoot=e)},get fallbackFormat(){return o.value?o.value.fallbackFormat:g},set fallbackFormat(e){o.value&&(o.value.fallbackFormat=e)},get warnHtmlMessage(){return o.value?o.value.warnHtmlMessage:b},set warnHtmlMessage(e){o.value&&(o.value.warnHtmlMessage=e)},get escapeParameter(){return o.value?o.value.escapeParameter:C},set escapeParameter(e){o.value&&(o.value.escapeParameter=e)},t:function(...e){return o.value?L((()=>Reflect.apply(o.value.t,null,[...e]))):L((()=>""))},getPostTranslationHandler:function(){return o.value?o.value.getPostTranslationHandler():E},setPostTranslationHandler:function(e){o.value&&o.value.setPostTranslationHandler(e)},getMissingHandler:function(){return o.value?o.value.getMissingHandler():d},setMissingHandler:function(e){o.value&&o.value.setMissingHandler(e)},rt:function(...e){return o.value?Reflect.apply(o.value.rt,null,[...e]):""},d:function(...e){return o.value?L((()=>Reflect.apply(o.value.d,null,[...e]))):L((()=>""))},n:function(...e){return o.value?L((()=>Reflect.apply(o.value.n,null,[...e]))):L((()=>""))},tm:function(e){return o.value?o.value.tm(e):{}},te:function(e,t){return!!o.value&&o.value.te(e,t)},getLocaleMessage:function(e){return o.value?o.value.getLocaleMessage(e):{}},setLocaleMessage:function(e,t){o.value&&(o.value.setLocaleMessage(e,t),c.value[e]=t)},mergeLocaleMessage:function(e,t){o.value&&o.value.mergeLocaleMessage(e,t)},getDateTimeFormat:function(e){return o.value?o.value.getDateTimeFormat(e):{}},setDateTimeFormat:function(e,t){o.value&&(o.value.setDateTimeFormat(e,t),u.value[e]=t)},mergeDateTimeFormat:function(e,t){o.value&&o.value.mergeDateTimeFormat(e,t)},getNumberFormat:function(e){return o.value?o.value.getNumberFormat(e):{}},setNumberFormat:function(e,t){o.value&&(o.value.setNumberFormat(e,t),m.value[e]=t)},mergeNumberFormat:function(e,t){o.value&&o.value.mergeNumberFormat(e,t)}};return onBeforeMount((()=>{if(null==e.proxy||null==e.proxy.$i18n)throw Error(I18nErrorCodes.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);const r=o.value=e.proxy.$i18n.__composer;"global"===t?(l.value=r.locale.value,i.value=r.fallbackLocale.value,c.value=r.messages.value,u.value=r.datetimeFormats.value,m.value=r.numberFormats.value):a&&function(e){e.locale.value=l.value,e.fallbackLocale.value=i.value,Object.keys(c.value).forEach((t=>{e.mergeLocaleMessage(t,c.value[t])})),Object.keys(u.value).forEach((t=>{e.mergeDateTimeFormat(t,u.value[t])})),Object.keys(m.value).forEach((t=>{e.mergeNumberFormat(t,m.value[t])})),e.escapeParameter=C,e.fallbackFormat=g,e.fallbackRoot=_,e.fallbackWarn=p,e.missingWarn=f,e.warnHtmlMessage=b}(r)})),N}const globalExportProps=["locale","fallbackLocale","availableLocales"],globalExportMethods=["t","rt","d","n","tm","te"];function injectGlobalFields(e,t){const r=Object.create(null);globalExportProps.forEach((e=>{const n=Object.getOwnPropertyDescriptor(t,e);if(!n)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);const a=isRef(n.value)?{get:()=>n.value.value,set(e){n.value.value=e}}:{get:()=>n.get&&n.get()};Object.defineProperty(r,e,a)})),e.config.globalProperties.$i18n=r,globalExportMethods.forEach((r=>{const n=Object.getOwnPropertyDescriptor(t,r);if(!n||!n.value)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${r}`,n)}));return()=>{delete e.config.globalProperties.$i18n,globalExportMethods.forEach((t=>{delete e.config.globalProperties[`$${t}`]}))}}registerMessageCompiler(compile),registerMessageResolver(resolveValue),registerLocaleFallbacker(fallbackWithLocaleChain);export{DatetimeFormat,I18nD,I18nInjectionKey,I18nN,I18nT,NumberFormat,Translation,VERSION,castToVueI18n,createI18n,useI18n,vTDirective};
