<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import HomePage from './components/HomePage.vue'
import ShuffleAnimation from './components/ShuffleAnimation.vue'
import CardSelection from './components/CardSelection.vue'
import UserForm from './components/UserForm.vue'
import ResultPage from './components/ResultPage.vue'

const { locale } = useI18n()

// 应用状态管理
const currentStep = ref('home') // home, shuffle, selection, form, result
const selectedCards = ref([])
const userInfo = ref({
  birthday: '',
  question: ''
})
const readingResult = ref('')

// 步骤切换函数
const goToStep = (step) => {
  currentStep.value = step
}

const handleStartReading = () => {
  goToStep('shuffle')
}

const handleShuffleComplete = () => {
  goToStep('selection')
}

const handleCardsSelected = (cards) => {
  selectedCards.value = cards
  goToStep('form')
}

const handleFormSubmit = (info) => {
  userInfo.value = info
  goToStep('result')
}

const handleBackToHome = () => {
  currentStep.value = 'home'
  selectedCards.value = []
  userInfo.value = { birthday: '', question: '' }
  readingResult.value = ''
}

// 语言切换
const toggleLanguage = () => {
  locale.value = locale.value === 'en' ? 'zh' : 'en'
}
</script>

<template>
  <div id="app">
    <!-- 语言切换按钮 -->
    <div class="language-toggle">
      <button @click="toggleLanguage" class="btn-language">
        {{ locale === 'en' ? '中文' : 'English' }}
      </button>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <HomePage
        v-if="currentStep === 'home'"
        @start-reading="handleStartReading"
      />

      <ShuffleAnimation
        v-if="currentStep === 'shuffle'"
        @shuffle-complete="handleShuffleComplete"
      />

      <CardSelection
        v-if="currentStep === 'selection'"
        @cards-selected="handleCardsSelected"
      />

      <UserForm
        v-if="currentStep === 'form'"
        @form-submit="handleFormSubmit"
        @back="goToStep('selection')"
      />

      <ResultPage
        v-if="currentStep === 'result'"
        :selected-cards="selectedCards"
        :user-info="userInfo"
        @back-to-home="handleBackToHome"
      />
    </div>
  </div>
</template>

<style scoped>
#app {
  position: relative;
  width: 100%;
  min-height: 100vh;
}

.language-toggle {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: 1000;
}

.btn-language {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.btn-language:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: var(--accent-primary);
}

.main-content {
  width: 100%;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
